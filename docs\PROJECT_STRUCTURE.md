# Project Structure

This document describes the organization and structure of the Credit-Based Multi-Channel Auto-Reaction Bot project.

## 📁 Directory Structure

```
credit-based-auto-reaction-bot/
├── app/                          # Main application code
│   ├── auto_reaction_bot.py      # Main bot implementation
│   ├── start_production.py      # Production startup script
│   ├── requirements.txt         # Python dependencies
│   └── src/                     # Source code modules
│       ├── services/            # Service layer
│       │   ├── credit_service.py
│       │   ├── channel_service.py
│       │   ├── webhook_service.py
│       │   └── userbot_manager.py
│       ├── workers/             # Background workers
│       │   └── enhanced_task_processor.py
│       ├── database/            # Database models and utilities
│       │   ├── models.py
│       │   └── connection.py
│       ├── utils/               # Utility functions
│       │   ├── encryption.py
│       │   ├── logging_config.py
│       │   └── validators.py
│       └── ui/                  # User interface components
│           ├── keyboards.py
│           ├── messages.py
│           └── handlers.py
├── config/                      # Configuration files
│   ├── .env                     # Environment variables (not in repo)
│   └── .env.example            # Environment template
├── scripts/                     # Management scripts
│   ├── docker-start.sh         # Docker startup script
│   ├── docker-stop.sh          # Docker shutdown script
│   └── docker-logs.sh          # Log viewing script
├── docs/                        # Documentation
│   └── PROJECT_STRUCTURE.md    # This file
├── data/                        # Application data (persistent)
├── logs/                        # Log files (persistent)
├── sessions/                    # Userbot session files (persistent)
├── mongo-init/                  # MongoDB initialization
│   └── init-db.js              # Database setup script
├── Dockerfile                   # Docker container definition
├── docker-compose.yml          # Docker orchestration
├── .dockerignore               # Docker ignore rules
├── README.md                   # Main project documentation
└── start.jpg                   # Welcome image
```

## 📦 Core Components

### Application Layer (`app/`)

#### Main Bot (`auto_reaction_bot.py`)
- Primary Telegram bot interface
- Command handlers and callback query processing
- User interaction management
- Real-time post detection via long polling

#### Production Starter (`start_production.py`)
- Orchestrates all system components
- Manages concurrent service startup
- Handles graceful shutdown
- Environment validation

#### Source Modules (`src/`)

**Services Layer:**
- `credit_service.py` - Credit management and transactions
- `channel_service.py` - Channel verification and management
- `webhook_service.py` - Post processing and task creation
- `userbot_manager.py` - Userbot session management

**Workers Layer:**
- `enhanced_task_processor.py` - Reaction task execution
- Background job processing
- Intelligent retry logic

**Database Layer:**
- `models.py` - Data models and schemas
- `connection.py` - Database connection management

**Utilities Layer:**
- `encryption.py` - Session file encryption
- `logging_config.py` - Centralized logging setup
- `validators.py` - Input validation functions

**UI Layer:**
- `keyboards.py` - Inline keyboard definitions
- `messages.py` - Message templates and formatting
- `handlers.py` - UI interaction handlers

### Configuration (`config/`)

#### Environment Configuration
- `.env.example` - Template with all available options
- `.env` - Actual configuration (not in repository)

**Key Configuration Categories:**
- Telegram API credentials
- Database connection strings
- Security keys and tokens
- Performance tuning parameters
- Feature flags and toggles

### Infrastructure (`scripts/`, Docker files)

#### Docker Configuration
- `Dockerfile` - Single-container application image
- `docker-compose.yml` - Complete service orchestration
- `.dockerignore` - Build optimization

#### Management Scripts
- `docker-start.sh` - Automated startup with health checks
- `docker-stop.sh` - Graceful shutdown
- `docker-logs.sh` - Log aggregation and viewing

### Data Persistence

#### Application Data (`data/`)
- User-generated content
- Temporary files
- Cache data

#### Logs (`logs/`)
- Application logs with rotation
- Error tracking
- Performance metrics

#### Sessions (`sessions/`)
- Encrypted userbot session files
- Session metadata
- Health status tracking

#### Database Initialization (`mongo-init/`)
- MongoDB setup scripts
- Index creation
- Initial data seeding

## 🔄 Data Flow

### Request Processing Flow
```
Telegram → Bot Interface → Service Layer → Database
                        ↓
                   Task Creation → Worker Pool → Userbot Sessions
```

### Component Interaction
```
Main Bot ←→ Webhook Service ←→ Task Processor
    ↓              ↓               ↓
Database ←→ Credit Service ←→ Userbot Manager
```

## 🚀 Deployment Architecture

### Single Container Deployment
- All components in one container
- Simplified management
- Optimal for small to medium scale

### Service Separation (Future)
- Microservice architecture
- Independent scaling
- Enhanced fault tolerance

## 📋 File Naming Conventions

### Python Files
- `snake_case` for all Python files
- Descriptive names indicating purpose
- Service suffix for service layer files

### Configuration Files
- Uppercase for environment variables
- Lowercase for configuration files
- `.example` suffix for templates

### Scripts
- Kebab-case for shell scripts
- Descriptive action names
- Platform-specific extensions

## 🔐 Security Considerations

### Sensitive Data
- Environment variables for secrets
- Encrypted session files
- No credentials in source code

### File Permissions
- Restricted access to session files
- Secure configuration file handling
- Docker user isolation

## 📈 Scalability Design

### Horizontal Scaling Points
- Worker processes
- Userbot sessions
- Database connections

### Performance Optimization
- Efficient database queries
- Connection pooling
- Caching strategies

## 🧪 Development Guidelines

### Code Organization
- Clear separation of concerns
- Modular design patterns
- Consistent naming conventions

### Testing Strategy
- Unit tests for core functions
- Integration tests for services
- End-to-end testing for workflows

### Documentation Standards
- Inline code documentation
- API documentation
- Architecture decision records

This structure provides a solid foundation for development, deployment, and maintenance of the credit-based multi-channel auto-reaction bot system.
