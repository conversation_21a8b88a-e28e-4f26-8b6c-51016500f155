#!/bin/bash
# Docker Start Script for Credit-Based Multi-Channel Auto-Reaction Bot

set -e

echo "🐳 Starting Credit-Based Multi-Channel Auto-Reaction Bot with Docker"
echo "=================================================================="

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Creating from template..."
    cp .env.docker .env
    echo "📝 Please edit .env file with your actual configuration values"
    echo "   Required: TELEGRAM_BOT_TOKEN, TELEGRAM_API_ID, TELEGRAM_API_HASH"
    echo ""
    read -p "Press Enter after updating .env file to continue..."
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p logs sessions data mongo-init

# Set proper permissions
echo "🔐 Setting proper permissions..."
chmod +x docker-start.sh docker-stop.sh docker-logs.sh
chmod 755 logs sessions data

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ docker-compose is not installed. Please install docker-compose and try again."
    exit 1
fi

echo "✅ Docker and docker-compose are available"

# Pull latest images
echo "📥 Pulling latest base images..."
docker-compose pull mongodb redis

# Build services
echo "🔨 Building bot services..."
docker-compose build --no-cache

# Start services
echo "🚀 Starting all services..."
docker-compose up -d

# Wait for services to be healthy
echo "⏳ Waiting for services to be healthy..."
sleep 10

# Check service status
echo "📊 Service Status:"
docker-compose ps

# Show logs
echo ""
echo "📋 Recent logs:"
docker-compose logs --tail=20

echo ""
echo "✅ Auto-Reaction Bot started successfully!"
echo ""
echo "🔗 Service URLs:"
echo "   - Main Bot: Running in background"
echo "   - API Service: http://localhost:8080"
echo "   - Flower Monitoring: http://localhost:5555 (admin:admin123)"
echo "   - MongoDB: localhost:27017"
echo "   - Redis: localhost:6379"
echo ""
echo "📋 Useful commands:"
echo "   - View logs: ./docker-logs.sh"
echo "   - Stop services: ./docker-stop.sh"
echo "   - Restart: docker-compose restart"
echo "   - Scale workers: docker-compose up -d --scale worker=3"
echo ""
echo "🎯 The bot is now ready to accept commands!"
