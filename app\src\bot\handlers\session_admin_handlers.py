#!/usr/bin/env python3
"""
Session Admin Handlers - Admin-only userbot session management interface
"""

import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes, Conversation<PERSON>andler, MessageHandler, filters
from typing import Dict, Any
from datetime import datetime

from src.services.enhanced_userbot_manager import EnhancedUserbotManager

logger = logging.getLogger(__name__)

# Conversation states
AWAITING_SESSION_NAME, AWAITING_PHONE_NUMBER, AWAITING_VERIFICATION_CODE, AWAITING_PASSWORD, AWAITING_SESSION_STRING = range(5)

class SessionAdminHandlers:
    """Admin-only handlers for userbot session management"""
    
    def __init__(self, userbot_manager: EnhancedUserbotManager):
        self.userbot_manager = userbot_manager
    
    async def show_sessions_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show main sessions management menu (admin only)"""
        
        user_id = update.effective_user.id
        
        if not await self.userbot_manager.check_admin_access(user_id):
            await update.message.reply_text("❌ Access denied. Admin privileges required.")
            return
        
        # Get session statistics
        stats = await self.userbot_manager.get_session_statistics()
        
        text = f"""
🤖 **Userbot Session Management**

**📊 Current Status:**
• **Total Sessions**: {stats['total_sessions']}
• **Active Sessions**: {stats['active_sessions']}
• **Healthy Sessions**: {stats['healthy_sessions']}
• **Unhealthy Sessions**: {stats['unhealthy_sessions']}
• **Reactions Today**: {stats['total_reactions_today']}
• **Optimal Concurrent Tasks**: {stats['optimal_concurrent_tasks']}

**⚡ Load Balancing:**
• **Average Load**: {stats['average_load']:.1f} tasks/session
• **Rate Limits**: {stats['rate_limits']['reactions_per_hour']}/hour, {stats['rate_limits']['reactions_per_day']}/day

Choose an action:
"""
        
        keyboard = [
            [
                InlineKeyboardButton("➕ Add Session", callback_data="session_add"),
                InlineKeyboardButton("📱 Add via Phone", callback_data="session_add_phone")
            ],
            [
                InlineKeyboardButton("📄 Import Session", callback_data="session_import"),
                InlineKeyboardButton("📱 QR Code", callback_data="session_qr")
            ],
            [
                InlineKeyboardButton("📊 View All Sessions", callback_data="session_list"),
                InlineKeyboardButton("🗑️ Remove Session", callback_data="session_remove")
            ],
            [
                InlineKeyboardButton("🔄 Refresh Stats", callback_data="session_refresh"),
                InlineKeyboardButton("⚙️ Auto-Scale Settings", callback_data="session_autoscale")
            ],
            [InlineKeyboardButton("🔙 Back to Admin", callback_data="admin_main")]
        ]
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        if update.callback_query:
            await update.callback_query.edit_message_text(text, reply_markup=reply_markup, parse_mode='Markdown')
        else:
            await update.message.reply_text(text, reply_markup=reply_markup, parse_mode='Markdown')
    
    async def handle_session_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle session management callbacks"""
        
        query = update.callback_query
        await query.answer()
        
        user_id = update.effective_user.id
        
        if not await self.userbot_manager.check_admin_access(user_id):
            await query.edit_message_text("❌ Access denied. Admin privileges required.")
            return
        
        data = query.data
        
        if data == "session_add_phone":
            await self._start_phone_session_creation(update, context)
        elif data == "session_import":
            await self._start_session_import(update, context)
        elif data == "session_qr":
            await self._generate_qr_code(update, context)
        elif data == "session_list":
            await self._show_session_list(update, context)
        elif data == "session_remove":
            await self._start_session_removal(update, context)
        elif data == "session_refresh":
            await self.show_sessions_menu(update, context)
        elif data == "session_autoscale":
            await self._show_autoscale_settings(update, context)
        elif data.startswith("remove_session_"):
            session_name = data.replace("remove_session_", "")
            await self._confirm_session_removal(update, context, session_name)
        elif data.startswith("confirm_remove_"):
            session_name = data.replace("confirm_remove_", "")
            await self._execute_session_removal(update, context, session_name)
    
    async def _start_phone_session_creation(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Start phone-based session creation process"""
        
        text = """
📱 **Add Session via Phone Number**

Please provide the following information:

**Step 1:** Enter a unique session name (e.g., "userbot_01")
**Step 2:** Enter phone number (with country code, e.g., +1234567890)
**Step 3:** Enter verification code sent to phone
**Step 4:** Enter 2FA password (if enabled)

Enter session name:
"""
        
        await update.callback_query.edit_message_text(text, parse_mode='Markdown')
        return AWAITING_SESSION_NAME
    
    async def handle_session_name_input(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle session name input"""
        
        session_name = update.message.text.strip()
        
        # Validate session name
        if not session_name.isalnum() and '_' not in session_name:
            await update.message.reply_text("❌ Session name must contain only letters, numbers, and underscores.")
            return AWAITING_SESSION_NAME
        
        context.user_data['session_name'] = session_name
        
        await update.message.reply_text(
            f"✅ Session name: `{session_name}`\n\n"
            "📱 Now enter the phone number (with country code, e.g., +1234567890):",
            parse_mode='Markdown'
        )
        
        return AWAITING_PHONE_NUMBER
    
    async def handle_phone_number_input(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle phone number input"""
        
        phone_number = update.message.text.strip()
        session_name = context.user_data['session_name']
        user_id = update.effective_user.id
        
        # Validate phone number format
        if not phone_number.startswith('+') or not phone_number[1:].replace(' ', '').isdigit():
            await update.message.reply_text("❌ Invalid phone number format. Use +1234567890")
            return AWAITING_PHONE_NUMBER
        
        # Start session creation
        result = await self.userbot_manager.create_session_via_phone(user_id, phone_number, session_name)
        
        if not result["success"]:
            await update.message.reply_text(result["message"])
            return ConversationHandler.END
        
        context.user_data['phone_number'] = phone_number
        
        await update.message.reply_text(
            f"✅ {result['message']}\n\n"
            "📱 Enter the verification code you received:",
            parse_mode='Markdown'
        )
        
        return AWAITING_VERIFICATION_CODE
    
    async def handle_verification_code_input(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle verification code input"""
        
        code = update.message.text.strip()
        session_name = context.user_data['session_name']
        user_id = update.effective_user.id
        
        # Verify code
        result = await self.userbot_manager.verify_phone_code(user_id, session_name, code)
        
        if result.get("requires_password"):
            await update.message.reply_text(
                "🔐 Two-factor authentication detected.\n\n"
                "Enter your 2FA password:"
            )
            context.user_data['verification_code'] = code
            return AWAITING_PASSWORD
        
        if result["success"]:
            session_info = result["session_info"]
            await update.message.reply_text(
                f"🎉 **Session Created Successfully!**\n\n"
                f"**Session Name:** `{session_info['session_name']}`\n"
                f"**User ID:** `{session_info['user_id']}`\n"
                f"**Username:** @{session_info['username']}\n"
                f"**Name:** {session_info['first_name']}\n"
                f"**Phone:** {session_info['phone_number']}\n\n"
                f"✅ Session is now active and ready for reactions!",
                parse_mode='Markdown'
            )
        else:
            await update.message.reply_text(result["message"])
        
        return ConversationHandler.END
    
    async def handle_password_input(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle 2FA password input"""
        
        password = update.message.text.strip()
        code = context.user_data['verification_code']
        session_name = context.user_data['session_name']
        user_id = update.effective_user.id
        
        # Verify with password
        result = await self.userbot_manager.verify_phone_code(user_id, session_name, code, password)
        
        if result["success"]:
            session_info = result["session_info"]
            await update.message.reply_text(
                f"🎉 **Session Created Successfully!**\n\n"
                f"**Session Name:** `{session_info['session_name']}`\n"
                f"**User ID:** `{session_info['user_id']}`\n"
                f"**Username:** @{session_info['username']}\n"
                f"**Name:** {session_info['first_name']}\n"
                f"**Phone:** {session_info['phone_number']}\n\n"
                f"✅ Session is now active and ready for reactions!",
                parse_mode='Markdown'
            )
        else:
            await update.message.reply_text(result["message"])
        
        return ConversationHandler.END
    
    async def _start_session_import(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Start session string import process"""
        
        text = """
📄 **Import Session String**

Please provide:

**Step 1:** Enter a unique session name
**Step 2:** Paste the session string

Enter session name:
"""
        
        await update.callback_query.edit_message_text(text, parse_mode='Markdown')
        return AWAITING_SESSION_NAME
    
    async def handle_session_string_input(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle session string input"""
        
        session_string = update.message.text.strip()
        session_name = context.user_data['session_name']
        user_id = update.effective_user.id
        
        # Import session
        result = await self.userbot_manager.import_session_string(user_id, session_name, session_string)
        
        if result["success"]:
            session_info = result["session_info"]
            await update.message.reply_text(
                f"🎉 **Session Imported Successfully!**\n\n"
                f"**Session Name:** `{session_info['session_name']}`\n"
                f"**User ID:** `{session_info['user_id']}`\n"
                f"**Username:** @{session_info['username']}\n"
                f"**Name:** {session_info['first_name']}\n"
                f"**Phone:** {session_info['phone_number']}\n\n"
                f"✅ Session is now active and ready for reactions!",
                parse_mode='Markdown'
            )
        else:
            await update.message.reply_text(result["message"])
        
        return ConversationHandler.END

    async def _generate_qr_code(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Generate QR code for session import"""

        user_id = update.effective_user.id
        result = await self.userbot_manager.generate_qr_code(user_id)

        if result["success"]:
            await update.callback_query.edit_message_text(
                "📱 **QR Code Generated**\n\n"
                "Scan this QR code with your Telegram app to import the session."
            )

            # Send QR code image
            await context.bot.send_photo(
                chat_id=update.effective_chat.id,
                photo=result["qr_code"],
                caption="📱 Scan this QR code with Telegram app"
            )
        else:
            await update.callback_query.edit_message_text(result["message"])

    async def _show_session_list(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show detailed list of all sessions"""

        user_id = update.effective_user.id
        result = await self.userbot_manager.get_detailed_session_info(user_id)

        if not result["success"]:
            await update.callback_query.edit_message_text(result["message"])
            return

        sessions = result["sessions"]
        summary = result["summary"]

        if not sessions:
            text = "📭 **No Active Sessions**\n\nNo userbot sessions are currently active."
        else:
            text = f"🤖 **Active Sessions ({len(sessions)})**\n\n"

            for i, session in enumerate(sessions[:10], 1):  # Show first 10 sessions
                status_emoji = "✅" if session["is_healthy"] else "❌"
                active_emoji = "🟢" if session["is_active"] else "🔴"

                text += f"**{i}. {session['session_name']}** {status_emoji}{active_emoji}\n"
                text += f"   👤 {session['first_name']} (@{session['username']})\n"
                text += f"   📱 {session['phone_number']}\n"
                text += f"   🎭 Reactions Today: {session['reactions_sent_today']}\n"
                text += f"   ⚡ Current Load: {session['current_load']}\n"
                text += f"   📊 Total Reactions: {session['reactions_sent_total']}\n"

                if session['flood_wait_until']:
                    text += f"   ⏳ Flood Wait Until: {session['flood_wait_until'][:19]}\n"

                text += "\n"

            if len(sessions) > 10:
                text += f"... and {len(sessions) - 10} more sessions\n\n"

            text += f"**📊 Summary:**\n"
            text += f"• Healthy: {summary['healthy_sessions']}/{summary['total_sessions']}\n"
            text += f"• Total Reactions Today: {summary['total_reactions_today']}\n"
            text += f"• Optimal Concurrent Tasks: {summary['optimal_concurrent_tasks']}\n"

        keyboard = [
            [InlineKeyboardButton("🔄 Refresh", callback_data="session_list")],
            [InlineKeyboardButton("🔙 Back", callback_data="session_menu")]
        ]

        reply_markup = InlineKeyboardMarkup(keyboard)
        await update.callback_query.edit_message_text(text, reply_markup=reply_markup, parse_mode='Markdown')

    async def _start_session_removal(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Start session removal process"""

        user_id = update.effective_user.id
        result = await self.userbot_manager.get_detailed_session_info(user_id)

        if not result["success"]:
            await update.callback_query.edit_message_text(result["message"])
            return

        sessions = result["sessions"]

        if not sessions:
            await update.callback_query.edit_message_text(
                "📭 **No Sessions to Remove**\n\nNo active sessions found."
            )
            return

        text = "🗑️ **Remove Session**\n\nSelect a session to remove:\n\n"

        keyboard = []
        for session in sessions[:20]:  # Show first 20 sessions
            status_emoji = "✅" if session["is_healthy"] else "❌"
            button_text = f"{session['session_name']} {status_emoji}"
            keyboard.append([InlineKeyboardButton(
                button_text,
                callback_data=f"remove_session_{session['session_name']}"
            )])

        keyboard.append([InlineKeyboardButton("🔙 Back", callback_data="session_menu")])

        reply_markup = InlineKeyboardMarkup(keyboard)
        await update.callback_query.edit_message_text(text, reply_markup=reply_markup, parse_mode='Markdown')

    async def _confirm_session_removal(self, update: Update, context: ContextTypes.DEFAULT_TYPE, session_name: str):
        """Confirm session removal"""

        text = f"""
⚠️ **Confirm Session Removal**

Are you sure you want to remove session: `{session_name}`?

**This action will:**
• Disconnect the userbot session
• Remove it from active sessions
• Mark it as inactive in database
• Stop all reaction tasks for this session

**This action cannot be undone.**
"""

        keyboard = [
            [
                InlineKeyboardButton("✅ Yes, Remove", callback_data=f"confirm_remove_{session_name}"),
                InlineKeyboardButton("❌ Cancel", callback_data="session_remove")
            ]
        ]

        reply_markup = InlineKeyboardMarkup(keyboard)
        await update.callback_query.edit_message_text(text, reply_markup=reply_markup, parse_mode='Markdown')

    async def _execute_session_removal(self, update: Update, context: ContextTypes.DEFAULT_TYPE, session_name: str):
        """Execute session removal"""

        user_id = update.effective_user.id
        result = await self.userbot_manager.remove_session(user_id, session_name)

        if result["success"]:
            text = f"✅ **Session Removed**\n\n{result['message']}\n\nThe session has been safely removed from the system."
        else:
            text = f"❌ **Removal Failed**\n\n{result['message']}"

        keyboard = [[InlineKeyboardButton("🔙 Back to Sessions", callback_data="session_menu")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.callback_query.edit_message_text(text, reply_markup=reply_markup, parse_mode='Markdown')

    async def _show_autoscale_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show auto-scaling settings"""

        stats = await self.userbot_manager.get_session_statistics()

        text = f"""
⚙️ **Auto-Scaling Settings**

**Current Configuration:**
• **Tasks per Session**: {self.userbot_manager.tasks_per_session}
• **Min Concurrent Tasks**: {self.userbot_manager.min_concurrent_tasks}
• **Max Concurrent Tasks**: {self.userbot_manager.max_concurrent_tasks}
• **Current Optimal**: {stats['optimal_concurrent_tasks']}

**Rate Limits:**
• **Reactions per Hour**: {self.userbot_manager.rate_limits['reactions_per_hour']}
• **Reactions per Day**: {self.userbot_manager.rate_limits['reactions_per_day']}
• **Flood Wait Threshold**: {self.userbot_manager.rate_limits['flood_wait_threshold']}s

**Auto-Scaling Formula:**
`Optimal Tasks = Active Sessions × Tasks per Session`
`Bounded by [Min, Max] limits`

**Current Status:**
• **Active Sessions**: {stats['active_sessions']}
• **Calculated Optimal**: {stats['active_sessions']} × {self.userbot_manager.tasks_per_session} = {stats['active_sessions'] * self.userbot_manager.tasks_per_session}
• **Applied Optimal**: {stats['optimal_concurrent_tasks']} (after bounds)
"""

        keyboard = [
            [InlineKeyboardButton("🔄 Refresh", callback_data="session_autoscale")],
            [InlineKeyboardButton("🔙 Back", callback_data="session_menu")]
        ]

        reply_markup = InlineKeyboardMarkup(keyboard)
        await update.callback_query.edit_message_text(text, reply_markup=reply_markup, parse_mode='Markdown')

    def get_conversation_handler(self):
        """Get conversation handler for session management"""

        return ConversationHandler(
            entry_points=[],  # Entry points handled by callback handlers
            states={
                AWAITING_SESSION_NAME: [
                    MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_session_name_input)
                ],
                AWAITING_PHONE_NUMBER: [
                    MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_phone_number_input)
                ],
                AWAITING_VERIFICATION_CODE: [
                    MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_verification_code_input)
                ],
                AWAITING_PASSWORD: [
                    MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_password_input)
                ],
                AWAITING_SESSION_STRING: [
                    MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_session_string_input)
                ]
            },
            fallbacks=[],
            per_chat=True,
            per_user=True
        )
