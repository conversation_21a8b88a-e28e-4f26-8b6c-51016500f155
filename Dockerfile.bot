# Dockerfile for Main Bot Service
FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/app
ENV SERVICE_TYPE=bot

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    libffi-dev \
    libssl-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Copy requirements and install dependencies
COPY code/requirements.txt /app/requirements.txt
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . /app/

# Create necessary directories
RUN mkdir -p /app/logs /app/sessions /app/data

# Create non-root user
RUN useradd -m -u 1000 botuser && \
    chown -R botuser:botuser /app
USER botuser

# Expose port for health checks
EXPOSE 8000

# Health check for bot service
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD python -c "import asyncio; import sys; sys.path.insert(0, '/app'); from auto_reaction_bot import CompleteCreditBasedAutoReactionBot; print('Bot healthy')" || exit 1

# Start main bot service
CMD ["python", "-c", "import asyncio; from auto_reaction_bot import CompleteCreditBasedAutoReactionBot; asyncio.run(CompleteCreditBasedAutoReactionBot().run())"]
