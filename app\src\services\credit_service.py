#!/usr/bin/env python3
"""
Credit Service - Atomic Credit Management System
"""

import asyncio
from typing import Optional, List, Tuple, Dict, Any
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorClientSession
from datetime import datetime, timezone, timedelta
from bson import ObjectId
import logging

logger = logging.getLogger(__name__)

class CreditService:
    """Service for managing user credits with atomic operations"""
    
    def __init__(self, db_client: AsyncIOMotorClient):
        self.client = db_client
        self.db = db_client.telegram_reaction_bot
        
    async def get_user_balance(self, user_id: int) -> int:
        """Get current credit balance for user"""
        try:
            user = await self.db.users.find_one({"telegram_user_id": user_id})
            return user.get("credit_balance", 0) if user else 0
        except Exception as e:
            logger.error(f"Error getting user balance for {user_id}: {e}")
            return 0
    
    async def add_credits(
        self, 
        user_id: int, 
        amount: int, 
        description: str,
        transaction_type: str = "purchase",
        payment_method: Optional[str] = None,
        payment_reference: Optional[str] = None
    ) -> Tuple[bool, Optional[str]]:
        """Add credits to user account with atomic transaction"""
        
        if amount <= 0:
            return False, "Amount must be positive"
        
        async with await self.client.start_session() as session:
            try:
                async with session.start_transaction():
                    # Get current balance
                    user = await self.db.users.find_one(
                        {"telegram_user_id": user_id}, 
                        session=session
                    )
                    
                    if not user:
                        return False, f"User {user_id} not found"
                    
                    current_balance = user.get("credit_balance", 0)
                    new_balance = current_balance + amount
                    
                    # Update user balance
                    await self.db.users.update_one(
                        {"telegram_user_id": user_id},
                        {
                            "$set": {
                                "credit_balance": new_balance,
                                "updated_at": datetime.now(timezone.utc)
                            },
                            "$inc": {
                                "total_credits_purchased": amount if transaction_type == "purchase" else 0
                            }
                        },
                        session=session
                    )
                    
                    # Create transaction record
                    transaction = {
                        "telegram_user_id": user_id,
                        "transaction_type": transaction_type,
                        "amount": amount,
                        "balance_before": current_balance,
                        "balance_after": new_balance,
                        "description": description,
                        "payment_method": payment_method,
                        "payment_reference": payment_reference,
                        "status": "completed",
                        "created_at": datetime.now(timezone.utc),
                        "processed_at": datetime.now(timezone.utc)
                    }
                    
                    result = await self.db.credit_transactions.insert_one(transaction, session=session)
                    
                    await session.commit_transaction()
                    logger.info(f"Added {amount} credits to user {user_id}. New balance: {new_balance}")
                    return True, str(result.inserted_id)
                    
            except Exception as e:
                await session.abort_transaction()
                logger.error(f"Error adding credits to user {user_id}: {e}")
                return False, str(e)
    
    async def consume_credits(
        self, 
        user_id: int, 
        amount: int, 
        description: str,
        reference_id: Optional[str] = None
    ) -> Tuple[bool, Optional[str]]:
        """Consume credits with atomic transaction"""
        
        if amount <= 0:
            return False, "Amount must be positive"
        
        async with await self.client.start_session() as session:
            try:
                async with session.start_transaction():
                    # Get current balance
                    user = await self.db.users.find_one(
                        {"telegram_user_id": user_id}, 
                        session=session
                    )
                    
                    if not user:
                        return False, "User not found"
                    
                    current_balance = user.get("credit_balance", 0)
                    
                    if current_balance < amount:
                        return False, f"Insufficient credits. Balance: {current_balance}, Required: {amount}"
                    
                    new_balance = current_balance - amount
                    
                    # Update user balance
                    await self.db.users.update_one(
                        {"telegram_user_id": user_id},
                        {
                            "$set": {
                                "credit_balance": new_balance,
                                "updated_at": datetime.now(timezone.utc)
                            },
                            "$inc": {
                                "total_credits_spent": amount
                            }
                        },
                        session=session
                    )
                    
                    # Create transaction record
                    transaction = {
                        "telegram_user_id": user_id,
                        "transaction_type": "consumption",
                        "amount": -amount,  # Negative for consumption
                        "balance_before": current_balance,
                        "balance_after": new_balance,
                        "description": description,
                        "reference_id": reference_id,
                        "status": "completed",
                        "created_at": datetime.now(timezone.utc),
                        "processed_at": datetime.now(timezone.utc)
                    }
                    
                    result = await self.db.credit_transactions.insert_one(transaction, session=session)
                    
                    await session.commit_transaction()
                    logger.info(f"Consumed {amount} credits from user {user_id}. New balance: {new_balance}")
                    return True, str(result.inserted_id)
                    
            except Exception as e:
                await session.abort_transaction()
                logger.error(f"Error consuming credits from user {user_id}: {e}")
                return False, str(e)
    
    async def refund_credits(
        self, 
        user_id: int, 
        amount: int, 
        description: str,
        original_transaction_id: Optional[str] = None
    ) -> Tuple[bool, Optional[str]]:
        """Refund credits to user account"""
        
        return await self.add_credits(
            user_id, 
            amount, 
            description, 
            transaction_type="refund",
            payment_reference=original_transaction_id
        )
    
    async def get_transaction_history(
        self, 
        user_id: int, 
        limit: int = 50,
        transaction_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get user's credit transaction history"""
        
        try:
            query = {"telegram_user_id": user_id}
            if transaction_type:
                query["transaction_type"] = transaction_type
            
            cursor = self.db.credit_transactions.find(query).sort("created_at", -1).limit(limit)
            return await cursor.to_list(length=limit)
        except Exception as e:
            logger.error(f"Error getting transaction history for user {user_id}: {e}")
            return []
    
    async def get_user_statistics(self, user_id: int) -> Dict[str, Any]:
        """Get comprehensive user credit statistics"""
        
        try:
            user = await self.db.users.find_one({"telegram_user_id": user_id})
            if not user:
                return {}
            
            # Get recent transactions
            recent_transactions = await self.get_transaction_history(user_id, limit=10)
            
            # Calculate daily/monthly spending
            now = datetime.now(timezone.utc)
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            
            daily_spent = await self.db.credit_transactions.aggregate([
                {
                    "$match": {
                        "telegram_user_id": user_id,
                        "transaction_type": "consumption",
                        "created_at": {"$gte": today_start}
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "total": {"$sum": {"$abs": "$amount"}}
                    }
                }
            ]).to_list(length=1)
            
            monthly_spent = await self.db.credit_transactions.aggregate([
                {
                    "$match": {
                        "telegram_user_id": user_id,
                        "transaction_type": "consumption",
                        "created_at": {"$gte": month_start}
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "total": {"$sum": {"$abs": "$amount"}}
                    }
                }
            ]).to_list(length=1)
            
            return {
                "current_balance": user.get("credit_balance", 0),
                "total_purchased": user.get("total_credits_purchased", 0),
                "total_spent": user.get("total_credits_spent", 0),
                "daily_spent": daily_spent[0]["total"] if daily_spent else 0,
                "monthly_spent": monthly_spent[0]["total"] if monthly_spent else 0,
                "recent_transactions": recent_transactions,
                "subscription_tier": user.get("subscription_tier", "free"),
                "max_channels": user.get("max_channels", 1),
                "max_reactions_per_post": user.get("max_reactions_per_post", 3)
            }
        except Exception as e:
            logger.error(f"Error getting user statistics for {user_id}: {e}")
            return {}
    
    async def check_sufficient_credits(self, user_id: int, required_amount: int) -> bool:
        """Check if user has sufficient credits"""
        balance = await self.get_user_balance(user_id)
        return balance >= required_amount
    
    async def get_system_credit_stats(self) -> Dict[str, Any]:
        """Get system-wide credit statistics"""
        
        try:
            # Total credits in system
            total_credits = await self.db.users.aggregate([
                {"$group": {"_id": None, "total": {"$sum": "$credit_balance"}}}
            ]).to_list(length=1)
            
            # Total transactions today
            today_start = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
            
            today_transactions = await self.db.credit_transactions.count_documents({
                "created_at": {"$gte": today_start}
            })
            
            # Total credits spent today
            today_spent = await self.db.credit_transactions.aggregate([
                {
                    "$match": {
                        "transaction_type": "consumption",
                        "created_at": {"$gte": today_start}
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "total": {"$sum": {"$abs": "$amount"}}
                    }
                }
            ]).to_list(length=1)
            
            return {
                "total_credits_in_system": total_credits[0]["total"] if total_credits else 0,
                "today_transactions": today_transactions,
                "today_credits_spent": today_spent[0]["total"] if today_spent else 0
            }
        except Exception as e:
            logger.error(f"Error getting system credit stats: {e}")
            return {}
