# Simplified Docker Compose for Auto-Reaction Bot
# This version uses a single container approach for easier deployment

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: auto-reaction-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD:-admin123}
      MONGO_INITDB_DATABASE: telegram_reaction_bot
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - bot-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis for Caching and Task Queue
  redis:
    image: redis:7.2-alpine
    container_name: auto-reaction-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - bot-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Complete Auto-Reaction Bot (All-in-One)
  bot:
    build:
      context: .
      dockerfile: Dockerfile.simple
    container_name: auto-reaction-bot-complete
    restart: unless-stopped
    environment:
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - TELEGRAM_API_ID=${TELEGRAM_API_ID}
      - TELEGRAM_API_HASH=${TELEGRAM_API_HASH}
      - MONGODB_URL=mongodb://${MONGO_ROOT_USERNAME:-admin}:${MONGO_ROOT_PASSWORD:-admin123}@mongodb:27017/telegram_reaction_bot?authSource=admin
      - REDIS_URL=redis://redis:6379/0
      - ADMIN_USER_IDS=${ADMIN_USER_IDS}
      - ENVIRONMENT=production
      - DEBUG=false
    ports:
      - "8000:8000"
    volumes:
      - ./sessions:/app/sessions
      - ./logs:/app/logs
      - ./data:/app/data
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - bot-network
    healthcheck:
      test: ["CMD", "python", "-c", "print('Bot healthy')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  bot-network:
    driver: bridge
