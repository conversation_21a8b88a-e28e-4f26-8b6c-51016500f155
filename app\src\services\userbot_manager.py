#!/usr/bin/env python3
"""
Userbot Manager - Scalable Multi-Session Infrastructure
"""

import asyncio
import random
import os
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timezone, timedelta
from motor.motor_asyncio import AsyncIOMotorClient
from pyrogram import Client
from pyrogram.errors import <PERSON><PERSON><PERSON>, SessionPasswordNeeded, AuthKeyUnregistered, ChannelInvalid
from bson import ObjectId
import logging

logger = logging.getLogger(__name__)

class UserbotManager:
    """Manages multiple userbot sessions with load balancing and health monitoring"""
    
    def __init__(self, db_client: AsyncIOMotorClient):
        self.db = db_client.telegram_reaction_bot
        self.active_sessions: Dict[str, Client] = {}
        self.session_stats: Dict[str, dict] = {}
        self.health_check_interval = 300  # 5 minutes
        self.api_id = int(os.getenv('TELEGRAM_API_ID'))
        self.api_hash = os.getenv('TELEGRAM_API_HASH')
        
    async def load_all_sessions(self) -> int:
        """Load all active userbot sessions"""
        
        logger.info("Loading all userbot sessions...")
        
        sessions = await self.db.userbot_sessions.find({
            "is_active": True,
            "is_banned": False
        }).to_list(length=None)
        
        loaded_count = 0
        
        for session_doc in sessions:
            try:
                success = await self.load_session(session_doc["session_name"])
                if success:
                    loaded_count += 1
                    logger.info(f"Loaded session: {session_doc['session_name']}")
            except Exception as e:
                logger.error(f"Failed to load session {session_doc['session_name']}: {e}")
                await self.mark_session_unhealthy(
                    session_doc["session_name"], 
                    f"Load error: {str(e)}"
                )
        
        logger.info(f"Successfully loaded {loaded_count} userbot sessions")
        return loaded_count
    
    async def load_session(self, session_name: str) -> bool:
        """Load a single userbot session"""
        
        try:
            session_doc = await self.db.userbot_sessions.find_one({
                "session_name": session_name
            })
            
            if not session_doc:
                logger.error(f"Session {session_name} not found in database")
                return False
            
            # Create Pyrogram client
            client = Client(
                name=session_name,
                api_id=self.api_id,
                api_hash=self.api_hash,
                workdir="sessions"
            )
            
            # Start client
            await client.start()
            
            # Verify session health
            me = await client.get_me()
            
            # Store active session
            self.active_sessions[session_name] = client
            self.session_stats[session_name] = {
                "user_id": me.id,
                "username": me.username,
                "first_name": me.first_name,
                "last_used": None,
                "reactions_sent_today": 0,
                "errors_count": 0,
                "is_healthy": True,
                "last_health_check": datetime.now(timezone.utc),
                "flood_wait_until": None
            }
            
            # Update database
            await self.db.userbot_sessions.update_one(
                {"session_name": session_name},
                {
                    "$set": {
                        "user_id": me.id,
                        "first_name": me.first_name,
                        "username": me.username,
                        "is_healthy": True,
                        "last_health_check": datetime.now(timezone.utc),
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            
            logger.info(f"Session {session_name} loaded successfully for user {me.first_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error loading session {session_name}: {e}")
            await self.mark_session_unhealthy(session_name, str(e))
            return False
    
    async def get_best_session(self, exclude_sessions: List[str] = None) -> Optional[str]:
        """Get the best available session for sending reactions"""
        
        if exclude_sessions is None:
            exclude_sessions = []
        
        # Filter healthy sessions
        available_sessions = []
        
        for session_name, stats in self.session_stats.items():
            if (session_name not in exclude_sessions and 
                stats["is_healthy"] and 
                session_name in self.active_sessions):
                
                # Check if session is in flood wait
                if stats.get("flood_wait_until"):
                    if datetime.now(timezone.utc) < stats["flood_wait_until"]:
                        continue  # Skip sessions in flood wait
                    else:
                        # Clear expired flood wait
                        stats["flood_wait_until"] = None
                
                # Check rate limits
                if await self.check_rate_limits(session_name):
                    available_sessions.append((session_name, stats))
        
        if not available_sessions:
            logger.warning("No available sessions for reaction sending")
            return None
        
        # Sort by usage (least used first) and randomize among top candidates
        available_sessions.sort(key=lambda x: x[1]["reactions_sent_today"])
        
        # Take top 3 least used sessions and pick randomly
        top_sessions = available_sessions[:min(3, len(available_sessions))]
        selected = random.choice(top_sessions)
        
        logger.debug(f"Selected session {selected[0]} for reaction")
        return selected[0]
    
    async def check_rate_limits(self, session_name: str) -> bool:
        """Check if session is within rate limits"""
        
        session_doc = await self.db.userbot_sessions.find_one({
            "session_name": session_name
        })
        
        if not session_doc:
            return False
        
        # Check daily limit
        daily_limit = session_doc.get("daily_limit", 1000)
        current_daily = session_doc.get("reactions_sent_today", 0)
        
        if current_daily >= daily_limit:
            logger.debug(f"Session {session_name} reached daily limit")
            return False
        
        # Check hourly limit
        hourly_limit = session_doc.get("hourly_limit", 100)
        current_hour_start = session_doc.get("current_hour_start")
        current_hour_count = session_doc.get("current_hour_count", 0)
        
        now = datetime.now(timezone.utc)
        
        # Reset hourly counter if new hour
        if (not current_hour_start or 
            now - current_hour_start > timedelta(hours=1)):
            
            await self.db.userbot_sessions.update_one(
                {"session_name": session_name},
                {
                    "$set": {
                        "current_hour_start": now,
                        "current_hour_count": 0
                    }
                }
            )
            return True
        
        return current_hour_count < hourly_limit
    
    async def send_reaction_with_session(
        self, 
        session_name: str, 
        channel_id: int, 
        message_id: int, 
        emoji: str
    ) -> Tuple[bool, Optional[str]]:
        """Send reaction using specific session"""
        
        if session_name not in self.active_sessions:
            return False, "Session not loaded"
        
        client = self.active_sessions[session_name]
        
        try:
            # Send reaction
            await client.send_reaction(
                chat_id=channel_id,
                message_id=message_id,
                emoji=emoji
            )
            
            # Update statistics
            await self.update_session_stats(session_name, success=True)
            
            logger.info(f"Reaction {emoji} sent successfully by {session_name} to {channel_id}:{message_id}")
            return True, None
            
        except FloodWait as e:
            # Handle flood wait
            await self.handle_flood_wait(session_name, e.value)
            return False, f"Flood wait: {e.value} seconds"
            
        except ChannelInvalid:
            # Channel access issue
            error_msg = "Channel access denied or invalid"
            await self.update_session_stats(session_name, success=False, error=error_msg)
            return False, error_msg
            
        except Exception as e:
            # Handle other errors
            error_msg = str(e)
            await self.update_session_stats(session_name, success=False, error=error_msg)
            logger.error(f"Error sending reaction with {session_name}: {error_msg}")
            return False, error_msg
    
    async def update_session_stats(
        self, 
        session_name: str, 
        success: bool, 
        error: Optional[str] = None
    ):
        """Update session statistics"""
        
        now = datetime.now(timezone.utc)
        
        # Update in-memory stats
        if session_name in self.session_stats:
            stats = self.session_stats[session_name]
            stats["last_used"] = now
            
            if success:
                stats["reactions_sent_today"] += 1
                stats["errors_count"] = max(0, stats["errors_count"] - 1)  # Reduce error count on success
            else:
                stats["errors_count"] += 1
                
                # Mark unhealthy if too many consecutive errors
                if stats["errors_count"] >= 5:
                    stats["is_healthy"] = False
                    logger.warning(f"Session {session_name} marked unhealthy due to errors")
        
        # Update database
        update_data = {
            "updated_at": now
        }
        
        if success:
            update_data["$inc"] = {
                "reactions_sent_today": 1,
                "reactions_sent_total": 1,
                "current_hour_count": 1
            }
            # Reset error count on successful reaction
            update_data["$set"] = {"errors_count": 0}
        else:
            update_data["$inc"] = {"errors_count": 1}
            update_data["$set"] = {"last_error": error}
            
            # Mark unhealthy if too many errors
            error_count = await self.db.userbot_sessions.find_one(
                {"session_name": session_name},
                {"errors_count": 1}
            )
            
            if error_count and error_count.get("errors_count", 0) >= 5:
                update_data["$set"]["is_healthy"] = False
        
        await self.db.userbot_sessions.update_one(
            {"session_name": session_name},
            update_data
        )
    
    async def handle_flood_wait(self, session_name: str, wait_time: int):
        """Handle flood wait by temporarily disabling session"""
        
        flood_wait_until = datetime.now(timezone.utc) + timedelta(seconds=wait_time + 10)
        
        # Update in-memory stats
        if session_name in self.session_stats:
            self.session_stats[session_name]["flood_wait_until"] = flood_wait_until
        
        # Update database
        await self.db.userbot_sessions.update_one(
            {"session_name": session_name},
            {
                "$set": {
                    "last_error": f"Flood wait: {wait_time}s",
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )
        
        logger.warning(f"Session {session_name} in flood wait for {wait_time} seconds")
    
    async def mark_session_unhealthy(self, session_name: str, reason: str):
        """Mark session as unhealthy"""
        
        await self.db.userbot_sessions.update_one(
            {"session_name": session_name},
            {
                "$set": {
                    "is_healthy": False,
                    "last_error": reason,
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )
        
        # Update in-memory stats
        if session_name in self.session_stats:
            self.session_stats[session_name]["is_healthy"] = False
        
        # Remove from active sessions if loaded
        if session_name in self.active_sessions:
            try:
                await self.active_sessions[session_name].stop()
            except:
                pass
            del self.active_sessions[session_name]
        
        logger.error(f"Session {session_name} marked unhealthy: {reason}")
    
    async def health_check_all_sessions(self):
        """Perform health check on all sessions"""
        
        logger.info("Performing health check on all sessions...")
        
        for session_name in list(self.active_sessions.keys()):
            try:
                client = self.active_sessions[session_name]
                
                # Simple health check - get own info
                me = await client.get_me()
                
                # Update health status
                await self.db.userbot_sessions.update_one(
                    {"session_name": session_name},
                    {
                        "$set": {
                            "last_health_check": datetime.now(timezone.utc),
                            "is_healthy": True
                        }
                    }
                )
                
                if session_name in self.session_stats:
                    self.session_stats[session_name]["last_health_check"] = datetime.now(timezone.utc)
                    self.session_stats[session_name]["is_healthy"] = True
                
                logger.debug(f"Health check passed for session {session_name}")
                
            except Exception as e:
                logger.error(f"Health check failed for session {session_name}: {e}")
                await self.mark_session_unhealthy(session_name, f"Health check failed: {str(e)}")
    
    async def get_session_statistics(self) -> Dict[str, Any]:
        """Get comprehensive session statistics"""
        
        total_sessions = await self.db.userbot_sessions.count_documents({})
        active_sessions = await self.db.userbot_sessions.count_documents({"is_active": True})
        healthy_sessions = await self.db.userbot_sessions.count_documents({
            "is_active": True,
            "is_healthy": True
        })
        
        # Calculate total reactions sent today
        today_start = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        
        daily_reactions = await self.db.userbot_sessions.aggregate([
            {
                "$match": {
                    "is_active": True
                }
            },
            {
                "$group": {
                    "_id": None,
                    "total": {"$sum": "$reactions_sent_today"}
                }
            }
        ]).to_list(length=1)
        
        return {
            "total_sessions": total_sessions,
            "active_sessions": active_sessions,
            "healthy_sessions": healthy_sessions,
            "loaded_sessions": len(self.active_sessions),
            "daily_reactions": daily_reactions[0]["total"] if daily_reactions else 0,
            "session_details": self.session_stats,
            "flood_wait_sessions": len([s for s in self.session_stats.values() 
                                      if s.get("flood_wait_until") and 
                                      datetime.now(timezone.utc) < s["flood_wait_until"]])
        }
    
    async def reset_daily_counters(self):
        """Reset daily reaction counters for all sessions"""
        
        await self.db.userbot_sessions.update_many(
            {},
            {
                "$set": {
                    "reactions_sent_today": 0,
                    "current_hour_count": 0,
                    "current_hour_start": datetime.now(timezone.utc)
                }
            }
        )
        
        # Reset in-memory stats
        for stats in self.session_stats.values():
            stats["reactions_sent_today"] = 0
        
        logger.info("Daily counters reset for all sessions")
    
    async def shutdown_all_sessions(self):
        """Gracefully shutdown all active sessions"""
        
        logger.info("Shutting down all userbot sessions...")
        
        for session_name, client in self.active_sessions.items():
            try:
                await client.stop()
                logger.info(f"Session {session_name} stopped")
            except Exception as e:
                logger.error(f"Error stopping session {session_name}: {e}")
        
        self.active_sessions.clear()
        self.session_stats.clear()
        
        logger.info("All userbot sessions shut down")

    async def start_health_monitoring(self):
        """Start background health monitoring task"""

        async def health_monitor():
            while True:
                try:
                    await asyncio.sleep(self.health_check_interval)
                    await self.health_check_all_sessions()
                except Exception as e:
                    logger.error(f"Health monitoring error: {e}")

        # Start health monitoring task
        asyncio.create_task(health_monitor())
        logger.info("Health monitoring started")

    async def start_daily_reset_scheduler(self):
        """Start daily counter reset scheduler"""

        async def daily_reset():
            while True:
                try:
                    # Calculate time until next midnight UTC
                    now = datetime.now(timezone.utc)
                    tomorrow = now.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
                    sleep_seconds = (tomorrow - now).total_seconds()

                    await asyncio.sleep(sleep_seconds)
                    await self.reset_daily_counters()
                except Exception as e:
                    logger.error(f"Daily reset error: {e}")

        # Start daily reset task
        asyncio.create_task(daily_reset())
        logger.info("Daily reset scheduler started")
