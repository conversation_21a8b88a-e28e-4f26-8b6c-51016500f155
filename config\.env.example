# =============================================================================
# TELEGRAM CONFIGURATION
# =============================================================================

# Main bot token from @BotFather
TELEGRAM_BOT_TOKEN=your_bot_token_here

# Telegram API credentials from https://my.telegram.org
TELEGRAM_API_ID=your_api_id_here
TELEGRAM_API_HASH=your_api_hash_here

# Webhook configuration (optional for development)
WEBHOOK_SECRET=your_webhook_secret_key_here

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# MongoDB connection string (Atlas or local)
MONGODB_URL=mongodb+srv://username:<EMAIL>/telegram_bot?retryWrites=true&w=majority

# Database name
DATABASE_NAME=telegram_reaction_bot

# MongoDB Docker Configuration (for Docker deployment)
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=admin123

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis connection for Celery and caching
REDIS_URL=redis://redis:6379/0

# Redis database numbers for different purposes
REDIS_CELERY_DB=0
REDIS_CACHE_DB=1

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# 32-byte encryption key for session files
ENCRYPTION_KEY=your_32_byte_encryption_key_here

# JWT secret for API authentication
JWT_SECRET=your_jwt_secret_key_here

# Session file encryption password
SESSION_PASSWORD=your_session_password_here

# =============================================================================
# ADMIN CONFIGURATION
# =============================================================================

# Comma-separated list of admin user IDs
ADMIN_USER_IDS=123456789,987654321

# Admin API token
ADMIN_API_TOKEN=your_admin_api_token_here

# Admin notification chat ID (for alerts)
ADMIN_CHAT_ID=-1001234567890

# =============================================================================
# USERBOT CONFIGURATION
# =============================================================================

# Number of userbot sessions to run
USERBOT_COUNT=30

# Proxy configuration (optional)
USE_PROXY=false
PROXY_TYPE=http
PROXY_HOST=proxy.example.com
PROXY_PORT=8080
PROXY_USERNAME=proxy_user
PROXY_PASSWORD=proxy_pass

# Rate limiting
MIN_REACTION_DELAY=0.5
MAX_REACTION_DELAY=1.5

# =============================================================================
# API CONFIGURATION
# =============================================================================

# FastAPI server configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4

# CORS settings
CORS_ORIGINS=http://localhost:3000,https://yourdomain.com

# =============================================================================
# CELERY CONFIGURATION
# =============================================================================

# Celery worker configuration
CELERY_WORKER_CONCURRENCY=10
CELERY_TASK_SOFT_TIME_LIMIT=300
CELERY_TASK_TIME_LIMIT=600

# Task retry configuration
MAX_RETRIES=3
RETRY_DELAY=60

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Log file path
LOG_FILE=logs/app.log

# Enable structured logging
STRUCTURED_LOGGING=true

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================

# Prometheus metrics
ENABLE_METRICS=true
METRICS_PORT=9090

# Health check configuration
HEALTH_CHECK_INTERVAL=60

# Alert thresholds
ALERT_FAILURE_RATE_THRESHOLD=0.05
ALERT_QUEUE_SIZE_THRESHOLD=1000
ALERT_OFFLINE_SESSIONS_THRESHOLD=3

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================

# Environment mode
ENVIRONMENT=production

# Debug mode
DEBUG=false

# Auto-reload for development
AUTO_RELOAD=false
