#!/usr/bin/env python3
"""
Reaction Logging Service
Logs all reaction attempts to reaction.log file with detailed information
"""

import os
import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional
import aiofiles

logger = logging.getLogger(__name__)

class ReactionLogger:
    """Service for logging reaction attempts to file"""
    
    def __init__(self, log_file_path: str = "reaction.log"):
        self.log_file_path = log_file_path
        self.ensure_log_directory()
    
    def ensure_log_directory(self):
        """Ensure the log directory exists"""
        log_dir = os.path.dirname(self.log_file_path)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
    
    async def log_reaction_attempt(
        self,
        channel_name: str,
        channel_id: int,
        post_id: int,
        emoji: str,
        status: str,
        user_id: int,
        credits_consumed: int = 1,
        error_message: Optional[str] = None,
        session_name: Optional[str] = None
    ):
        """Log a reaction attempt to the reaction.log file"""
        
        try:
            timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S UTC")
            
            # Format: [timestamp] [channel_name] [post_id] [emoji] [status] [user_credits_consumed] [additional_info]
            log_entry = (
                f"[{timestamp}] "
                f"[{channel_name}] "
                f"[{post_id}] "
                f"[{emoji}] "
                f"[{status}] "
                f"[user:{user_id}] "
                f"[credits:{credits_consumed}]"
            )
            
            # Add optional information
            if session_name:
                log_entry += f" [session:{session_name}]"
            
            if error_message:
                log_entry += f" [error:{error_message}]"
            
            log_entry += f" [channel_id:{channel_id}]"
            log_entry += "\n"
            
            # Write to file asynchronously
            async with aiofiles.open(self.log_file_path, mode='a', encoding='utf-8') as f:
                await f.write(log_entry)
            
            # Also log to application logger
            if status == "SUCCESS":
                logger.info(f"Reaction logged: {channel_name} - {emoji} - {status}")
            else:
                logger.warning(f"Reaction failed: {channel_name} - {emoji} - {status} - {error_message}")
                
        except Exception as e:
            logger.error(f"Error logging reaction attempt: {e}")
    
    async def log_reaction_success(
        self,
        channel_name: str,
        channel_id: int,
        post_id: int,
        emoji: str,
        user_id: int,
        session_name: Optional[str] = None
    ):
        """Log a successful reaction"""
        await self.log_reaction_attempt(
            channel_name=channel_name,
            channel_id=channel_id,
            post_id=post_id,
            emoji=emoji,
            status="SUCCESS",
            user_id=user_id,
            credits_consumed=1,
            session_name=session_name
        )
    
    async def log_reaction_failure(
        self,
        channel_name: str,
        channel_id: int,
        post_id: int,
        emoji: str,
        user_id: int,
        error_message: str,
        session_name: Optional[str] = None
    ):
        """Log a failed reaction"""
        await self.log_reaction_attempt(
            channel_name=channel_name,
            channel_id=channel_id,
            post_id=post_id,
            emoji=emoji,
            status="FAILED",
            user_id=user_id,
            credits_consumed=0,  # No credits consumed on failure
            error_message=error_message,
            session_name=session_name
        )
    
    async def log_reaction_retry(
        self,
        channel_name: str,
        channel_id: int,
        post_id: int,
        emoji: str,
        user_id: int,
        attempt_number: int,
        session_name: Optional[str] = None
    ):
        """Log a reaction retry attempt"""
        await self.log_reaction_attempt(
            channel_name=channel_name,
            channel_id=channel_id,
            post_id=post_id,
            emoji=emoji,
            status=f"RETRY_{attempt_number}",
            user_id=user_id,
            credits_consumed=0,  # No credits consumed on retry
            session_name=session_name
        )
    
    async def log_insufficient_credits(
        self,
        channel_name: str,
        channel_id: int,
        post_id: int,
        emoji: str,
        user_id: int,
        required_credits: int,
        available_credits: int
    ):
        """Log insufficient credits scenario"""
        await self.log_reaction_attempt(
            channel_name=channel_name,
            channel_id=channel_id,
            post_id=post_id,
            emoji=emoji,
            status="INSUFFICIENT_CREDITS",
            user_id=user_id,
            credits_consumed=0,
            error_message=f"Required:{required_credits}, Available:{available_credits}"
        )
    
    async def log_channel_paused(
        self,
        channel_name: str,
        channel_id: int,
        post_id: int,
        user_id: int,
        reason: str = "Auto-reaction disabled"
    ):
        """Log when a channel is paused or auto-reaction is disabled"""
        await self.log_reaction_attempt(
            channel_name=channel_name,
            channel_id=channel_id,
            post_id=post_id,
            emoji="N/A",
            status="CHANNEL_PAUSED",
            user_id=user_id,
            credits_consumed=0,
            error_message=reason
        )
    
    async def get_recent_logs(self, lines: int = 100) -> list:
        """Get recent log entries"""
        
        try:
            if not os.path.exists(self.log_file_path):
                return []
            
            async with aiofiles.open(self.log_file_path, mode='r', encoding='utf-8') as f:
                content = await f.read()
                log_lines = content.strip().split('\n')
                return log_lines[-lines:] if log_lines else []
                
        except Exception as e:
            logger.error(f"Error reading log file: {e}")
            return []
    
    async def get_log_statistics(self) -> Dict[str, Any]:
        """Get statistics from the log file"""
        
        try:
            logs = await self.get_recent_logs(1000)  # Last 1000 entries
            
            if not logs:
                return {
                    "total_attempts": 0,
                    "successful_reactions": 0,
                    "failed_reactions": 0,
                    "success_rate": 0,
                    "total_credits_consumed": 0
                }
            
            total_attempts = len(logs)
            successful_reactions = len([log for log in logs if "[SUCCESS]" in log])
            failed_reactions = len([log for log in logs if "[FAILED]" in log])
            
            # Calculate total credits consumed
            total_credits = 0
            for log in logs:
                if "[credits:" in log:
                    try:
                        credits_part = log.split("[credits:")[1].split("]")[0]
                        total_credits += int(credits_part)
                    except:
                        pass
            
            success_rate = (successful_reactions / total_attempts * 100) if total_attempts > 0 else 0
            
            return {
                "total_attempts": total_attempts,
                "successful_reactions": successful_reactions,
                "failed_reactions": failed_reactions,
                "success_rate": success_rate,
                "total_credits_consumed": total_credits,
                "retry_attempts": len([log for log in logs if "RETRY_" in log]),
                "insufficient_credits": len([log for log in logs if "INSUFFICIENT_CREDITS" in log]),
                "channel_paused": len([log for log in logs if "CHANNEL_PAUSED" in log])
            }
            
        except Exception as e:
            logger.error(f"Error calculating log statistics: {e}")
            return {}
    
    async def cleanup_old_logs(self, max_lines: int = 10000):
        """Keep only the most recent log entries"""
        
        try:
            if not os.path.exists(self.log_file_path):
                return
            
            async with aiofiles.open(self.log_file_path, mode='r', encoding='utf-8') as f:
                content = await f.read()
                log_lines = content.strip().split('\n')
            
            if len(log_lines) > max_lines:
                # Keep only the most recent entries
                recent_lines = log_lines[-max_lines:]
                
                async with aiofiles.open(self.log_file_path, mode='w', encoding='utf-8') as f:
                    await f.write('\n'.join(recent_lines) + '\n')
                
                logger.info(f"Cleaned up log file, kept {len(recent_lines)} recent entries")
                
        except Exception as e:
            logger.error(f"Error cleaning up log file: {e}")

# Global reaction logger instance
reaction_logger = ReactionLogger()
