#!/usr/bin/env python3
"""
Enhanced Database Models for Credit-Based Multi-Channel System
"""

from datetime import datetime, timezone
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from bson import ObjectId

class PyObjectId(ObjectId):
    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid objectid")
        return ObjectId(v)

    @classmethod
    def __modify_schema__(cls, field_schema):
        field_schema.update(type="string")

class EnhancedUser(BaseModel):
    """Enhanced user model with credit system"""
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    telegram_user_id: int
    username: Optional[str] = None
    first_name: str
    last_name: Optional[str] = None
    
    # Credit System
    credit_balance: int = 0
    total_credits_purchased: int = 0
    total_credits_spent: int = 0
    
    # Subscription & Limits
    subscription_tier: str = "free"  # free, basic, premium, enterprise
    max_channels: int = 1
    max_reactions_per_post: int = 3
    
    # Status & Timestamps
    is_active: bool = True
    is_banned: bool = False
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    last_activity: Optional[datetime] = None

    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}

class Channel(BaseModel):
    """Channel management model"""
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    channel_id: int  # Telegram channel ID
    channel_username: Optional[str] = None
    channel_title: str
    
    # Ownership & Access
    owner_user_id: int  # Telegram user ID of owner
    added_by_user_id: int  # User who added the channel
    verification_status: str = "pending"  # pending, verified, failed
    verification_token: Optional[str] = None
    
    # Channel Settings
    is_active: bool = True
    reactions_per_post: int = 1
    emoji_list: List[str] = ["👍", "❤️", "🔥"]
    reaction_mode: str = "random"  # random, fixed, sequential
    reaction_delay_min: int = 5  # seconds
    reaction_delay_max: int = 30  # seconds
    
    # Statistics
    total_posts_processed: int = 0
    total_reactions_sent: int = 0
    total_credits_consumed: int = 0
    last_post_processed: Optional[datetime] = None
    
    # Status & Timestamps
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}

class CreditTransaction(BaseModel):
    """Credit transaction logging"""
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    user_id: int  # Telegram user ID
    transaction_type: str  # purchase, consumption, refund, bonus
    amount: int  # Positive for credits added, negative for consumed
    balance_before: int
    balance_after: int
    
    # Transaction Details
    description: str
    reference_id: Optional[str] = None  # Channel ID, task ID, etc.
    payment_method: Optional[str] = None  # stripe, paypal, crypto, admin
    payment_reference: Optional[str] = None
    
    # Status & Timestamps
    status: str = "completed"  # pending, completed, failed, refunded
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    processed_at: Optional[datetime] = None

    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}

class EnhancedReactionTask(BaseModel):
    """Enhanced reaction task model"""
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    
    # Task Details
    channel_id: int
    message_id: int
    emoji: str
    user_id: int  # Owner of the channel
    channel_doc_id: str  # Reference to Channel document
    
    # Processing
    assigned_session: Optional[str] = None
    attempts: int = 0
    max_attempts: int = 3
    
    # Credit Management
    credits_consumed: int = 1
    credit_transaction_id: Optional[str] = None
    
    # Status & Timestamps
    status: str = "pending"  # pending, processing, completed, failed, cancelled
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    scheduled_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None

    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}

class EnhancedUserbotSession(BaseModel):
    """Enhanced userbot session model"""
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    session_name: str
    phone_number: str
    
    # Session Details
    user_id: int
    first_name: str
    last_name: Optional[str] = None
    username: Optional[str] = None
    
    # Performance & Health
    is_active: bool = True
    is_healthy: bool = True
    last_health_check: Optional[datetime] = None
    reactions_sent_today: int = 0
    reactions_sent_total: int = 0
    errors_count: int = 0
    last_error: Optional[str] = None
    
    # Rate Limiting
    daily_limit: int = 1000
    hourly_limit: int = 100
    current_hour_count: int = 0
    current_hour_start: Optional[datetime] = None
    
    # Proxy & Security
    proxy_config: Optional[Dict[str, Any]] = None
    is_banned: bool = False
    ban_reason: Optional[str] = None
    
    # Timestamps
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}

class ChannelPost(BaseModel):
    """Channel post tracking for automation"""
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    channel_id: int
    message_id: int
    channel_doc_id: str  # Reference to Channel document
    
    # Post Details
    post_text: Optional[str] = None
    post_type: str = "text"  # text, photo, video, document
    post_date: datetime
    
    # Processing Status
    is_processed: bool = False
    reactions_to_send: int = 0
    reactions_sent: int = 0
    tasks_created: List[str] = []  # List of ReactionTask IDs
    
    # Timestamps
    discovered_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    processed_at: Optional[datetime] = None

    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}

class Notification(BaseModel):
    """User notification model"""
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    user_id: int
    message: str
    notification_type: str = "info"  # info, warning, error, success
    status: str = "pending"  # pending, sent, failed
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    sent_at: Optional[datetime] = None

    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}
