# Dockerfile for Task Processor Worker Service
FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/app
ENV SERVICE_TYPE=worker

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    libffi-dev \
    libssl-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Copy requirements and install dependencies
COPY code/requirements.txt /app/requirements.txt
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . /app/

# Create necessary directories
RUN mkdir -p /app/logs /app/sessions /app/data

# Create non-root user
RUN useradd -m -u 1000 botuser && \
    chown -R botuser:botuser /app
USER botuser

# Health check for worker service
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD python -c "import sys; sys.path.insert(0, '/app/code'); from src.workers.enhanced_task_processor import EnhancedTaskProcessor; print('Worker healthy')" || exit 1

# Start task processor worker
CMD ["python", "-c", "import asyncio; import sys; import os; sys.path.insert(0, '/app/code'); from motor.motor_asyncio import AsyncIOMotorClient; from src.workers.enhanced_task_processor import EnhancedTaskProcessor; client = AsyncIOMotorClient(os.getenv('MONGODB_URL')); processor = EnhancedTaskProcessor(client); asyncio.run(processor.start_processing())"]
