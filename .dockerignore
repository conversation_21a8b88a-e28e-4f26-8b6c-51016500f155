# Docker ignore file for Auto-Reaction Bot

# Git
.git
.gitignore
.gitattributes

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs (will be mounted as volumes)
logs/
*.log

# Sessions (will be mounted as volumes)
sessions/*.session
sessions/*.session-journal

# Data (will be mounted as volumes)
data/

# Environment files
.env
.env.local
.env.production

# Documentation
README.md
docs/
*.md

# Test files
tests/
test_*
*_test.py

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Node modules (if any)
node_modules/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/
