#!/bin/bash
# Docker Stop Script for Credit-Based Multi-Channel Auto-Reaction Bot

set -e

echo "🛑 Stopping Credit-Based Multi-Channel Auto-Reaction Bot"
echo "======================================================="

# Check if docker-compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ docker-compose is not installed."
    exit 1
fi

# Stop all services gracefully
echo "⏹️  Stopping all services..."
docker-compose stop

# Show final status
echo "📊 Final service status:"
docker-compose ps

echo ""
echo "✅ All services stopped successfully!"
echo ""
echo "💡 To completely remove containers and volumes:"
echo "   docker-compose down -v"
echo ""
echo "💡 To start again:"
echo "   ./docker-start.sh"
