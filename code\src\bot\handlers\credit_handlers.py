#!/usr/bin/env python3
"""
Credit Purchase System with Crypto Payment Integration
Complete UI/UX for credit purchasing and payment processing
"""

import os
import sys
import asyncio
import logging
import qrcode
import io
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone, timedelta
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from motor.motor_asyncio import AsyncIOMotorClient

# Add src to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, '..', '..')
sys.path.insert(0, src_dir)

from services.credit_service import CreditService

logger = logging.getLogger(__name__)

class CreditHandlers:
    """Complete credit purchase and payment system"""
    
    def __init__(self, db_client: AsyncIOMotorClient):
        self.db = db_client.telegram_reaction_bot
        self.credit_service = CreditService(db_client)
        
        # Credit packages with pricing
        self.credit_packages = {
            "500": {"credits": 500, "price": 5.00, "bonus": 0},
            "1000": {"credits": 1000, "price": 9.00, "bonus": 100},
            "3000": {"credits": 3000, "price": 25.00, "bonus": 500},
            "10000": {"credits": 10000, "price": 75.00, "bonus": 2000},
            "50000": {"credits": 50000, "price": 300.00, "bonus": 15000},
            "100000": {"credits": 100000, "price": 500.00, "bonus": 35000},
            "500000": {"credits": 500000, "price": 2000.00, "bonus": 200000},
            "1000000": {"credits": 1000000, "price": 3500.00, "bonus": 500000},
            "10000000": {"credits": 10000000, "price": 25000.00, "bonus": 7500000}
        }
        
        # Crypto wallet addresses (example - replace with real addresses)
        self.crypto_wallets = {
            "USDT_TRC20": "TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE",
            "USDT_ERC20": "******************************************",
            "BTC": "******************************************",
            "ETH": "******************************************"
        }
    
    async def show_buy_credits_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show credit purchase main menu"""
        
        query = update.callback_query
        user_id = update.effective_user.id
        
        # Get current balance
        current_balance = await self.credit_service.get_user_balance(user_id)
        
        text = f"""
💰 **BUY CREDITS**

**Current Balance:** {current_balance:,} credits

**Why Buy Credits?**
• Each reaction costs 1 credit
• Automatic reactions to all your channel posts
• No monthly fees - pay only for what you use
• Bulk packages include bonus credits
• Instant activation after payment

**💡 Credit Usage Examples:**
• 1,000 credits = 1,000 reactions
• Average channel: 50-200 reactions/day
• Popular channel: 500-2,000 reactions/day

**🎁 Bonus Credits:**
Larger packages include FREE bonus credits!

**Select a package to continue:**
"""
        
        keyboard = [
            [
                InlineKeyboardButton("500 Credits - $5.00", callback_data="credits:package:500"),
                InlineKeyboardButton("1,000 Credits - $9.00 🎁", callback_data="credits:package:1000")
            ],
            [
                InlineKeyboardButton("3,000 Credits - $25.00 🎁", callback_data="credits:package:3000"),
                InlineKeyboardButton("10K Credits - $75.00 🎁", callback_data="credits:package:10000")
            ],
            [
                InlineKeyboardButton("50K Credits - $300 🎁", callback_data="credits:package:50000"),
                InlineKeyboardButton("100K Credits - $500 🎁", callback_data="credits:package:100000")
            ],
            [
                InlineKeyboardButton("500K Credits - $2,000 🎁", callback_data="credits:package:500000"),
                InlineKeyboardButton("1M Credits - $3,500 🎁", callback_data="credits:package:1000000")
            ],
            [
                InlineKeyboardButton("10M Credits - $25,000 🎁", callback_data="credits:package:10000000")
            ],
            [
                InlineKeyboardButton("💳 Payment History", callback_data="credits:history"),
                InlineKeyboardButton("🔙 Back to Menu", callback_data="menu:main")
            ]
        ]
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
    
    async def show_package_confirmation(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show package confirmation with detailed breakdown"""
        
        query = update.callback_query
        package_id = query.data.split(":")[-1]
        
        if package_id not in self.credit_packages:
            await query.answer("Invalid package selected", show_alert=True)
            return
        
        package = self.credit_packages[package_id]
        total_credits = package["credits"] + package["bonus"]
        savings = f" (Save ${(package['credits'] * 0.01) - package['price']:.2f}!)" if package["bonus"] > 0 else ""
        
        text = f"""
💰 **CONFIRM PURCHASE**

**Package Details:**
📦 **Base Credits:** {package['credits']:,}
🎁 **Bonus Credits:** {package['bonus']:,}
✨ **Total Credits:** {total_credits:,}
💵 **Price:** ${package['price']:.2f}{savings}

**📊 Value Breakdown:**
• Cost per credit: ${package['price'] / total_credits:.4f}
• Bonus value: ${package['bonus'] * 0.01:.2f} FREE
• You save: {(package['bonus'] / package['credits'] * 100):.0f}% extra credits

**⚡ What You Get:**
• Instant credit delivery
• {total_credits:,} automatic reactions
• No expiration date
• 24/7 system availability
• Full analytics and tracking

**Ready to purchase {total_credits:,} credits for ${package['price']:.2f}?**
"""
        
        keyboard = [
            [InlineKeyboardButton(f"✅ Continue to Buy {total_credits:,} Credits", 
                                callback_data=f"credits:confirm:{package_id}")],
            [
                InlineKeyboardButton("🔙 Back to Packages", callback_data="menu:buy_credits"),
                InlineKeyboardButton("🏠 Main Menu", callback_data="menu:main")
            ]
        ]
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
    
    async def show_payment_methods(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show available payment methods"""
        
        query = update.callback_query
        package_id = query.data.split(":")[-1]
        
        if package_id not in self.credit_packages:
            await query.answer("Invalid package selected", show_alert=True)
            return
        
        package = self.credit_packages[package_id]
        total_credits = package["credits"] + package["bonus"]
        
        text = f"""
💳 **PAYMENT METHODS**

**Order Summary:**
📦 Package: {total_credits:,} credits
💵 Total: ${package['price']:.2f}

**Available Payment Methods:**

🪙 **CRYPTOCURRENCY** (Recommended)
• Instant processing
• Lower fees
• Secure blockchain transactions
• USDT, BTC, ETH supported

💳 **Traditional Payments**
• Credit/Debit cards
• PayPal
• Bank transfers
• Coming soon...

**Select your preferred payment method:**
"""
        
        keyboard = [
            [InlineKeyboardButton("🪙 PAY WITH CRYPTO", callback_data=f"payment:crypto:{package_id}")],
            [InlineKeyboardButton("💳 More payment methods coming soon...", callback_data="payment:coming_soon")],
            [
                InlineKeyboardButton("🔙 Back", callback_data=f"credits:package:{package_id}"),
                InlineKeyboardButton("🏠 Main Menu", callback_data="menu:main")
            ]
        ]
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
    
    async def show_crypto_payment(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show cryptocurrency payment options"""
        
        query = update.callback_query
        package_id = query.data.split(":")[-1]
        
        if package_id not in self.credit_packages:
            await query.answer("Invalid package selected", show_alert=True)
            return
        
        package = self.credit_packages[package_id]
        total_credits = package["credits"] + package["bonus"]
        
        text = f"""
🪙 **CRYPTOCURRENCY PAYMENT**

**Order Details:**
📦 Credits: {total_credits:,}
💵 Amount: ${package['price']:.2f}

**Select Cryptocurrency:**

**🔥 RECOMMENDED:**
• USDT (TRC20) - Lowest fees (~$1)
• USDT (ERC20) - Standard fees (~$5-20)

**🪙 POPULAR:**
• Bitcoin (BTC) - Higher fees (~$10-50)
• Ethereum (ETH) - Variable fees (~$5-30)

**💡 Tips:**
• TRC20 USDT has the lowest transaction fees
• All payments are processed automatically
• Credits are added within 10-30 minutes
"""
        
        keyboard = [
            [
                InlineKeyboardButton("💚 USDT (TRC20) - Low Fees", 
                                   callback_data=f"crypto:usdt_trc20:{package_id}"),
                InlineKeyboardButton("💙 USDT (ERC20)", 
                                   callback_data=f"crypto:usdt_erc20:{package_id}")
            ],
            [
                InlineKeyboardButton("🟠 Bitcoin (BTC)", 
                                   callback_data=f"crypto:btc:{package_id}"),
                InlineKeyboardButton("🔷 Ethereum (ETH)", 
                                   callback_data=f"crypto:eth:{package_id}")
            ],
            [
                InlineKeyboardButton("🔙 Back to Payment Methods", 
                                   callback_data=f"credits:confirm:{package_id}"),
                InlineKeyboardButton("🏠 Main Menu", callback_data="menu:main")
            ]
        ]
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
    
    async def show_crypto_payment_details(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show specific cryptocurrency payment details with QR code"""
        
        query = update.callback_query
        data_parts = query.data.split(":")
        crypto_type = data_parts[1]
        package_id = data_parts[2]
        
        if package_id not in self.credit_packages:
            await query.answer("Invalid package selected", show_alert=True)
            return
        
        package = self.credit_packages[package_id]
        total_credits = package["credits"] + package["bonus"]
        
        # Get wallet address
        wallet_key = crypto_type.upper()
        wallet_address = self.crypto_wallets.get(wallet_key, "Address not available")
        
        # Create order ID
        order_id = f"ORD_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{package_id}"
        
        # Crypto names and networks
        crypto_info = {
            "usdt_trc20": {"name": "USDT", "network": "TRC20 (Tron)", "symbol": "USDT"},
            "usdt_erc20": {"name": "USDT", "network": "ERC20 (Ethereum)", "symbol": "USDT"},
            "btc": {"name": "Bitcoin", "network": "Bitcoin Network", "symbol": "BTC"},
            "eth": {"name": "Ethereum", "network": "Ethereum Network", "symbol": "ETH"}
        }
        
        crypto = crypto_info.get(crypto_type, {"name": "Unknown", "network": "Unknown", "symbol": "?"})
        
        text = f"""
🪙 **{crypto['name']} PAYMENT**

**📋 Order Information:**
🆔 Order ID: `{order_id}`
📦 Credits: {total_credits:,}
💵 Amount: ${package['price']:.2f} USD
🪙 Pay: ${package['price']:.2f} {crypto['symbol']}

**📤 Payment Instructions:**

**1️⃣ Copy Wallet Address:**
`{wallet_address}`

**2️⃣ Send Payment:**
• Network: {crypto['network']}
• Amount: ${package['price']:.2f} {crypto['symbol']}
• Address: {wallet_address}

**3️⃣ Confirmation:**
• Payment processed automatically
• Credits added within 10-30 minutes
• You'll receive a confirmation message

**⚠️ Important:**
• Send exact amount: ${package['price']:.2f} {crypto['symbol']}
• Use correct network: {crypto['network']}
• Double-check wallet address
• Keep transaction hash for reference

**🔍 Order Status:**
Waiting for payment...
"""
        
        keyboard = [
            [InlineKeyboardButton("📋 Copy Wallet Address", 
                                callback_data=f"copy:address:{crypto_type}")],
            [InlineKeyboardButton("🔄 Check Payment Status", 
                                callback_data=f"payment:status:{order_id}")],
            [
                InlineKeyboardButton("❓ Payment Help", 
                                   callback_data="payment:help"),
                InlineKeyboardButton("🔙 Back", 
                                   callback_data=f"payment:crypto:{package_id}")
            ],
            [InlineKeyboardButton("🏠 Main Menu", callback_data="menu:main")]
        ]
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        # Try to generate and send QR code
        try:
            # Generate QR code
            qr = qrcode.QRCode(version=1, box_size=10, border=5)
            qr.add_data(wallet_address)
            qr.make(fit=True)
            
            # Create QR code image
            qr_img = qr.make_image(fill_color="black", back_color="white")
            
            # Convert to bytes
            bio = io.BytesIO()
            qr_img.save(bio, 'PNG')
            bio.seek(0)
            
            # Send QR code with payment details
            await query.message.reply_photo(
                photo=bio,
                caption=text,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
            
            # Delete the original message
            await query.delete_message()
            
        except Exception as e:
            logger.error(f"Error generating QR code: {e}")
            # Fallback to text message
            await query.edit_message_text(
                text,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
        
        # Store order in database
        try:
            order_doc = {
                "order_id": order_id,
                "user_id": update.effective_user.id,
                "package_id": package_id,
                "credits": total_credits,
                "amount_usd": package['price'],
                "crypto_type": crypto_type,
                "wallet_address": wallet_address,
                "status": "pending",
                "created_at": datetime.now(timezone.utc),
                "expires_at": datetime.now(timezone.utc) + timedelta(hours=24)
            }
            
            await self.db.payment_orders.insert_one(order_doc)
            logger.info(f"Created payment order {order_id} for user {update.effective_user.id}")
            
        except Exception as e:
            logger.error(f"Error storing payment order: {e}")
    
    async def show_payment_help(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show payment help and support information"""
        
        query = update.callback_query
        
        text = """
❓ **PAYMENT HELP & SUPPORT**

**🔧 Common Issues:**

**1️⃣ Payment Not Detected**
• Wait 10-30 minutes for confirmation
• Check if you sent the exact amount
• Verify you used the correct network
• Ensure wallet address is correct

**2️⃣ Wrong Network Used**
• Contact support with transaction hash
• We'll help resolve the issue
• May take 24-48 hours to process

**3️⃣ Sent Wrong Amount**
• Contact support immediately
• Provide transaction hash
• We'll credit the correct amount

**📞 Support Channels:**
• Telegram: @YourSupportBot
• Email: <EMAIL>
• Response time: 2-24 hours

**💡 Tips for Faster Processing:**
• Use recommended USDT (TRC20)
• Send exact amount shown
• Double-check wallet address
• Keep transaction hash

**🔒 Security:**
• All payments are secure
• Blockchain verified
• No personal data stored
• Automatic processing
"""
        
        keyboard = [
            [InlineKeyboardButton("📞 Contact Support", url="https://t.me/YourSupportBot")],
            [InlineKeyboardButton("🔙 Back to Payment", callback_data="menu:buy_credits")]
        ]
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
