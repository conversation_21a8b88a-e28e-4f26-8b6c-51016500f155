#!/usr/bin/env python3
"""
Worker Pool Scaling Script for Auto-Reaction Bot System
"""

import os
import sys
import argparse

def update_task_processor_concurrency(new_limit):
    """Update max_concurrent_tasks in EnhancedTaskProcessor"""
    
    file_path = "app/src/workers/enhanced_task_processor.py"
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Replace the max_concurrent_tasks line
    old_line = "self.max_concurrent_tasks = 50"
    new_line = f"self.max_concurrent_tasks = {new_limit}"
    
    if old_line in content:
        content = content.replace(old_line, new_line)
        
        with open(file_path, 'w') as f:
            f.write(content)
        
        print(f"✅ Updated task processor concurrency to {new_limit}")
        return True
    else:
        print(f"❌ Could not find line to update in {file_path}")
        return False

def update_env_variable(key, value):
    """Update environment variable in config/.env"""
    
    env_path = "config/.env"
    
    if not os.path.exists(env_path):
        print(f"❌ Environment file not found: {env_path}")
        return False
    
    with open(env_path, 'r') as f:
        lines = f.readlines()
    
    updated = False
    for i, line in enumerate(lines):
        if line.startswith(f"{key}="):
            lines[i] = f"{key}={value}\n"
            updated = True
            break
    
    if not updated:
        lines.append(f"{key}={value}\n")
    
    with open(env_path, 'w') as f:
        f.writelines(lines)
    
    print(f"✅ Updated {key}={value}")
    return True

def scale_for_scenario(scenario):
    """Scale system for specific usage scenario"""
    
    scenarios = {
        "10k": {
            "max_concurrent_tasks": 50,
            "userbot_count": 30,
            "api_workers": 4,
            "description": "10K daily reactions"
        },
        "100k": {
            "max_concurrent_tasks": 200,
            "userbot_count": 100,
            "api_workers": 6,
            "description": "100K daily reactions"
        },
        "1m": {
            "max_concurrent_tasks": 500,
            "userbot_count": 200,
            "api_workers": 8,
            "description": "1M+ daily reactions"
        }
    }
    
    if scenario not in scenarios:
        print(f"❌ Unknown scenario: {scenario}")
        print(f"Available scenarios: {', '.join(scenarios.keys())}")
        return False
    
    config = scenarios[scenario]
    print(f"🚀 Scaling system for {config['description']}...")
    
    # Update task processor concurrency
    if not update_task_processor_concurrency(config["max_concurrent_tasks"]):
        return False
    
    # Update environment variables
    if not update_env_variable("USERBOT_COUNT", config["userbot_count"]):
        return False
    
    if not update_env_variable("API_WORKERS", config["api_workers"]):
        return False
    
    print(f"✅ System scaled for {config['description']}")
    print("\n📋 Next steps:")
    print("1. docker-compose down")
    print("2. docker-compose build bot")
    print("3. docker-compose up -d")
    
    return True

def main():
    parser = argparse.ArgumentParser(description="Scale Auto-Reaction Bot Worker Pools")
    parser.add_argument("scenario", choices=["10k", "100k", "1m"], 
                       help="Scaling scenario (10k, 100k, 1m)")
    
    args = parser.parse_args()
    
    if not scale_for_scenario(args.scenario):
        sys.exit(1)

if __name__ == "__main__":
    main()
