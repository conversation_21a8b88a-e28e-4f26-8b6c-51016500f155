# Credit-Based Multi-Channel Auto-Reaction Bot

[![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://python.org)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://docker.com)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Status](https://img.shields.io/badge/Status-Production%20Ready-brightgreen.svg)]()

A comprehensive, enterprise-grade Telegram bot system for automated reactions across multiple channels with a sophisticated credit-based economy, scalable userbot infrastructure, and advanced management features.

## 🚀 Features

### Core Functionality
- **💰 Credit-Based Economy**: Pay-per-reaction system with comprehensive transaction tracking
- **📺 Multi-Channel Management**: Support for unlimited channels with individual settings and verification
- **🤖 Scalable Userbot Infrastructure**: 30-200 concurrent userbot sessions with health monitoring
- **⚡ Real-Time Post Detection**: Long polling for immediate reaction deployment
- **🎛️ Advanced Admin Panel**: Complete control panel with analytics, monitoring, and user management
- **🐳 Docker Containerization**: Production-ready deployment with Docker Compose

### Advanced Features
- **🔄 Intelligent Task Scheduling**: Smart retry logic and load balancing
- **📊 Comprehensive Analytics**: Detailed reaction statistics and performance metrics
- **🔐 Security Features**: Encrypted session management and secure API endpoints
- **📱 Mobile-Optimized UI**: Responsive inline keyboard interface
- **🌐 Webhook Integration**: External API support for third-party integrations
- **📈 Scalable Architecture**: Horizontal scaling support for high-volume operations

## 📋 Prerequisites

### System Requirements
- **Python**: 3.11 or higher
- **Docker**: Latest version with Docker Compose
- **Memory**: Minimum 2GB RAM (4GB+ recommended for production)
- **Storage**: 10GB+ available disk space
- **Network**: Stable internet connection with unrestricted access to Telegram APIs

### Required Services
- **MongoDB**: Atlas cloud instance or local MongoDB server
- **Redis**: For caching and task queue management
- **Telegram Bot**: Bot token from [@BotFather](https://t.me/BotFather)
- **Telegram API**: API credentials from [my.telegram.org](https://my.telegram.org)

## 🛠️ Installation & Setup

### Method 1: Docker Deployment (Recommended)

1. **Clone the Repository**
   ```bash
   git clone <repository-url>
   cd credit-based-auto-reaction-bot
   ```

2. **Configure Environment**
   ```bash
   cp config/.env.example config/.env
   nano config/.env  # Edit with your credentials
   ```

3. **Start the System**
   ```bash
   # Make scripts executable
   chmod +x scripts/*.sh

   # Start all services
   docker-compose up -d
   ```

4. **Verify Deployment**
   ```bash
   # Check service status
   docker-compose ps

   # View logs
   docker-compose logs -f
   ```

### Method 2: Manual Installation

1. **Install Dependencies**
   ```bash
   pip install -r app/requirements.txt
   ```

2. **Configure Environment**
   ```bash
   cp config/.env.example config/.env
   # Edit config/.env with your settings
   ```

3. **Start Services**
   ```bash
   # Start MongoDB and Redis (if local)
   # Then start the bot
   python app/start_production.py
   ```

## ⚙️ Configuration

### Environment Variables

The system uses environment variables for configuration. Copy `config/.env.example` to `config/.env` and configure the following:

#### Essential Configuration
```env
# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_bot_token_from_botfather
TELEGRAM_API_ID=your_api_id_from_my_telegram_org
TELEGRAM_API_HASH=your_api_hash_from_my_telegram_org

# Database Configuration
MONGODB_URL=mongodb+srv://user:<EMAIL>/db
REDIS_URL=redis://redis:6379/0

# Admin Configuration
ADMIN_USER_IDS=123456789,987654321
```

#### Advanced Configuration
```env
# Userbot Settings
USERBOT_COUNT=30
MIN_REACTION_DELAY=0.5
MAX_REACTION_DELAY=1.5

# Security Settings
ENCRYPTION_KEY=your_32_byte_encryption_key
JWT_SECRET=your_jwt_secret_key

# Performance Settings
CELERY_WORKER_CONCURRENCY=10
MAX_RETRIES=3
```

### Docker Configuration

The system includes a streamlined single-container deployment approach for optimal performance and easier management.

## 🎯 Usage

### Getting Started

1. **Start the Bot**: Send `/start` to your bot on Telegram
2. **Add Channels**: Forward a message from your channel to the bot
3. **Verify Ownership**: Complete the channel verification process
4. **Configure Reactions**: Set up reaction preferences and timing
5. **Add Credits**: Purchase credits for reaction services
6. **Monitor Performance**: Use the admin panel to track activity

### Bot Commands

- `/start` - Initialize the bot and show welcome message
- `/menu` - Access the main control panel
- `/help` - Display comprehensive help information
- `/status` - Check system status and statistics
- `/credits` - View credit balance and transaction history
- `/channels` - Manage your channels and settings
- `/admin` - Access admin panel (admin users only)

### Admin Features

Administrators have access to additional features:
- User management and credit allocation
- System monitoring and analytics
- Global settings and configuration
- Performance metrics and logs
- Emergency controls and maintenance mode

## 🏗️ Architecture

### System Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Telegram Bot  │    │  Task Processor │    │ Webhook Service │
│   (Interface)   │◄──►│  (Workers)      │◄──►│ (Processing)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    MongoDB      │    │     Redis       │    │  Credit System  │
│  (Database)     │    │   (Cache)       │    │ (Transactions)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Data Flow

1. **Post Detection**: Main bot detects new channel posts via long polling
2. **Task Creation**: Webhook service processes posts and creates reaction tasks
3. **Task Execution**: Worker pool executes tasks using available userbot sessions
4. **Credit Processing**: Credits are consumed only after successful reactions
5. **Monitoring**: All activities are logged and monitored in real-time

### Scaling Strategy

- **Horizontal Scaling**: Add more worker containers for increased throughput
- **Session Management**: Dynamic userbot session allocation and health monitoring
- **Load Balancing**: Intelligent task distribution across available workers
- **Resource Optimization**: Automatic scaling based on queue size and performance metrics

## 🔧 Management & Monitoring

### Docker Management

```bash
# View service status
docker-compose ps

# View logs
scripts/docker-logs.sh [service_name]

# Restart services
docker-compose restart

# Update system
docker-compose pull && docker-compose up -d
```

### Health Monitoring

The system includes comprehensive health checks:
- **Service Health**: All containers have health check endpoints
- **Database Connectivity**: MongoDB and Redis connection monitoring
- **Userbot Status**: Session health and availability tracking
- **Performance Metrics**: Response times and throughput monitoring

### Troubleshooting

#### Common Issues

1. **Services Not Starting**
   ```bash
   # Check logs
   docker-compose logs [service_name]

   # Verify configuration
   docker-compose config

   # Restart services
   docker-compose restart
   ```

2. **Database Connection Issues**
   ```bash
   # Test MongoDB connection
   docker-compose exec mongodb mongosh

   # Test Redis connection
   docker-compose exec redis redis-cli ping
   ```

3. **Userbot Session Problems**
   ```bash
   # Check session files
   ls -la sessions/

   # View logs
   docker-compose logs bot
   ```

## 📊 API Documentation

### Webhook Endpoints

The system exposes several API endpoints for external integration:

- `POST /webhook/telegram` - Telegram webhook endpoint
- `GET /health` - Health check endpoint
- `GET /metrics` - Prometheus metrics endpoint
- `POST /api/v1/reactions` - Manual reaction trigger
- `GET /api/v1/stats` - System statistics

### Authentication

API endpoints require authentication via JWT tokens or API keys configured in the environment.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check this README and inline code documentation
- **Issues**: Report bugs and feature requests via GitHub Issues
- **Community**: Join our community discussions
- **Commercial Support**: Contact us for enterprise support and customization

## 🔄 Changelog

### v1.0.0 (Current)
- Initial production release
- Complete credit-based reaction system
- Docker containerization
- Multi-channel management
- Scalable userbot infrastructure
- Advanced admin panel

---

**Made with ❤️ for the Telegram automation community**