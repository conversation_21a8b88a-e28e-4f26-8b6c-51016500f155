# Dockerfile for API/Webhook Service
FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/app
ENV SERVICE_TYPE=api

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    libffi-dev \
    libssl-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Copy requirements and install dependencies
COPY code/requirements.txt /app/requirements.txt
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir fastapi uvicorn

# Copy application code
COPY . /app/

# Create necessary directories
RUN mkdir -p /app/logs /app/sessions /app/data

# Create non-root user
RUN useradd -m -u 1000 botuser && \
    chown -R botuser:botuser /app
USER botuser

# Expose API port
EXPOSE 8080

# Health check for API service
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Start webhook/API service
CMD ["python", "-c", "import asyncio; import sys; import os; sys.path.insert(0, '/app/code'); from motor.motor_asyncio import AsyncIOMotorClient; from src.services.webhook_service import WebhookService; client = AsyncIOMotorClient(os.getenv('MONGODB_URL')); service = WebhookService(client); asyncio.run(service.start_background_services())"]
