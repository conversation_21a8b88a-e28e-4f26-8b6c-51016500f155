# Docker Environment Configuration for Auto-Reaction Bot
# Copy this file to .env and fill in your actual values

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_API_ID=your_api_id_here
TELEGRAM_API_HASH=your_api_hash_here

# Admin Configuration
ADMIN_USER_IDS=123456789,987654321

# MongoDB Configuration
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=admin123
MONGODB_URL=*****************************************************************************

# Redis Configuration
REDIS_URL=redis://redis:6379/0

# Service Configuration
ENVIRONMENT=production
LOG_LEVEL=INFO

# Security Configuration (Optional)
JWT_SECRET_KEY=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here

# Monitoring Configuration
FLOWER_BASIC_AUTH=admin:admin123

# Scaling Configuration
WORKER_REPLICAS=2
MAX_CONCURRENT_TASKS=50

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3
