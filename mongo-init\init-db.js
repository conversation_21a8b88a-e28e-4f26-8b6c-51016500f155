// MongoDB Initialization Script for Auto-Reaction Bot
// This script creates the database and initial collections with proper indexes

// Switch to the telegram_reaction_bot database
db = db.getSiblingDB('telegram_reaction_bot');

// Create collections with validation
db.createCollection('users', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['telegram_user_id', 'created_at'],
      properties: {
        telegram_user_id: {
          bsonType: 'long',
          description: 'Telegram user ID must be a long and is required'
        },
        username: {
          bsonType: 'string',
          description: 'Telegram username'
        },
        first_name: {
          bsonType: 'string',
          description: 'User first name'
        },
        credits: {
          bsonType: 'int',
          minimum: 0,
          description: 'User credit balance'
        },
        subscription_plan: {
          bsonType: 'string',
          enum: ['free', 'basic', 'premium', 'enterprise'],
          description: 'User subscription plan'
        },
        created_at: {
          bsonType: 'date',
          description: 'Account creation date'
        }
      }
    }
  }
});

db.createCollection('channels', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['channel_id', 'owner_user_id', 'created_at'],
      properties: {
        channel_id: {
          bsonType: 'long',
          description: 'Telegram channel ID'
        },
        owner_user_id: {
          bsonType: 'long',
          description: 'Owner telegram user ID'
        },
        channel_title: {
          bsonType: 'string',
          description: 'Channel title'
        },
        verification_status: {
          bsonType: 'string',
          enum: ['pending', 'verified', 'failed'],
          description: 'Channel verification status'
        },
        is_active: {
          bsonType: 'bool',
          description: 'Channel active status'
        },
        auto_reaction_enabled: {
          bsonType: 'bool',
          description: 'Auto-reaction enabled status'
        }
      }
    }
  }
});

db.createCollection('reaction_tasks');
db.createCollection('credit_transactions');
db.createCollection('userbot_sessions');
db.createCollection('channel_posts');

// Create indexes for better performance
print('Creating indexes...');

// Users collection indexes
db.users.createIndex({ 'telegram_user_id': 1 }, { unique: true });
db.users.createIndex({ 'username': 1 });
db.users.createIndex({ 'created_at': 1 });

// Channels collection indexes
db.channels.createIndex({ 'channel_id': 1, 'owner_user_id': 1 }, { unique: true });
db.channels.createIndex({ 'owner_user_id': 1 });
db.channels.createIndex({ 'verification_status': 1 });
db.channels.createIndex({ 'is_active': 1 });
db.channels.createIndex({ 'auto_reaction_enabled': 1 });

// Reaction tasks indexes
db.reaction_tasks.createIndex({ 'status': 1 });
db.reaction_tasks.createIndex({ 'telegram_user_id': 1 });
db.reaction_tasks.createIndex({ 'channel_id': 1 });
db.reaction_tasks.createIndex({ 'created_at': 1 });
db.reaction_tasks.createIndex({ 'scheduled_for': 1 });

// Credit transactions indexes
db.credit_transactions.createIndex({ 'telegram_user_id': 1 });
db.credit_transactions.createIndex({ 'transaction_type': 1 });
db.credit_transactions.createIndex({ 'created_at': 1 });

// Userbot sessions indexes
db.userbot_sessions.createIndex({ 'session_name': 1 }, { unique: true });
db.userbot_sessions.createIndex({ 'is_active': 1 });
db.userbot_sessions.createIndex({ 'last_used': 1 });

// Channel posts indexes
db.channel_posts.createIndex({ 'channel_id': 1, 'message_id': 1 }, { unique: true });
db.channel_posts.createIndex({ 'discovered_at': 1 });

print('Database initialization completed successfully!');
print('Collections created: users, channels, reaction_tasks, credit_transactions, userbot_sessions, channel_posts');
print('Indexes created for optimal performance');

// Create a test user (optional)
db.users.insertOne({
  telegram_user_id: NumberLong(123456789),
  username: 'test_user',
  first_name: 'Test',
  credits: 1000,
  subscription_plan: 'premium',
  max_channels: 10,
  created_at: new Date(),
  updated_at: new Date()
});

print('Test user created with ID: 123456789');
print('MongoDB initialization complete!');
