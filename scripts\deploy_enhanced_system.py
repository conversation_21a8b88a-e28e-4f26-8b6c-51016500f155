#!/usr/bin/env python3
"""
Enhanced System Deployment Script
Deploys the production-ready admin-only userbot session management system
"""

import os
import sys
import subprocess
import asyncio
from pathlib import Path

def print_banner():
    """Print deployment banner"""
    print("=" * 80)
    print("🚀 ENHANCED USERBOT SESSION MANAGEMENT SYSTEM DEPLOYMENT")
    print("=" * 80)
    print("📋 Features:")
    print("  • Admin-only access control with authentication")
    print("  • Encrypted persistent session storage in MongoDB")
    print("  • Auto-scaling concurrent tasks based on active sessions")
    print("  • Production-grade health monitoring and recovery")
    print("  • SaaS-quality reliability and security")
    print("  • Zero-downtime session management")
    print("=" * 80)

def check_prerequisites():
    """Check system prerequisites"""
    print("\n🔍 Checking Prerequisites...")
    
    # Check Docker
    try:
        result = subprocess.run(['docker', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Docker installed:", result.stdout.strip())
        else:
            print("❌ Docker not found")
            return False
    except FileNotFoundError:
        print("❌ Docker not installed")
        return False
    
    # Check Docker Compose
    try:
        result = subprocess.run(['docker-compose', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Docker Compose installed:", result.stdout.strip())
        else:
            print("❌ Docker Compose not found")
            return False
    except FileNotFoundError:
        print("❌ Docker Compose not installed")
        return False
    
    # Check required files
    required_files = [
        'config/.env',
        'app/auto_reaction_bot.py',
        'app/src/services/enhanced_userbot_manager.py',
        'app/src/bot/handlers/session_admin_handlers.py',
        'docker-compose.yml'
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ Found: {file_path}")
        else:
            print(f"❌ Missing: {file_path}")
            return False
    
    return True

def validate_environment():
    """Validate environment configuration"""
    print("\n🔧 Validating Environment Configuration...")
    
    env_file = Path('config/.env')
    if not env_file.exists():
        print("❌ Environment file not found: config/.env")
        return False
    
    required_vars = [
        'TELEGRAM_BOT_TOKEN',
        'TELEGRAM_API_ID',
        'TELEGRAM_API_HASH',
        'ADMIN_USER_IDS',
        'MONGODB_URL'
    ]
    
    env_content = env_file.read_text()
    missing_vars = []
    
    for var in required_vars:
        if f"{var}=" not in env_content:
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        return False
    
    print("✅ All required environment variables found")
    
    # Check if encryption key is set
    if "ENCRYPTION_KEY=" not in env_content:
        print("⚠️  ENCRYPTION_KEY not set - will generate new key (not recommended for production)")
        print("   Set ENCRYPTION_KEY to a 32-byte base64 encoded key for production")
    else:
        print("✅ Encryption key configured")
    
    return True

def build_system():
    """Build the enhanced system"""
    print("\n🔨 Building Enhanced System...")
    
    try:
        # Stop existing containers
        print("🛑 Stopping existing containers...")
        subprocess.run(['docker-compose', 'down'], check=False)
        
        # Build new containers
        print("🔨 Building containers...")
        result = subprocess.run(['docker-compose', 'build', 'bot'], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Build completed successfully")
            return True
        else:
            print("❌ Build failed:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Build error: {e}")
        return False

def deploy_system():
    """Deploy the enhanced system"""
    print("\n🚀 Deploying Enhanced System...")
    
    try:
        # Start services
        result = subprocess.run(['docker-compose', 'up', '-d'], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Services started successfully")
            return True
        else:
            print("❌ Deployment failed:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Deployment error: {e}")
        return False

def verify_deployment():
    """Verify the deployment"""
    print("\n✅ Verifying Deployment...")
    
    try:
        # Check service status
        result = subprocess.run(['docker-compose', 'ps'], capture_output=True, text=True)
        print("📊 Service Status:")
        print(result.stdout)
        
        # Check logs for startup success
        print("\n📋 Recent Logs:")
        result = subprocess.run(['docker-compose', 'logs', 'bot', '--tail=20'], capture_output=True, text=True)
        
        if "System ready for 100,000+ daily reactions!" in result.stdout:
            print("✅ System startup successful")
            return True
        else:
            print("⚠️  System may still be starting up")
            print("Recent logs:")
            print(result.stdout)
            return True
            
    except Exception as e:
        print(f"❌ Verification error: {e}")
        return False

def show_usage_instructions():
    """Show usage instructions"""
    print("\n" + "=" * 80)
    print("🎉 ENHANCED SESSION MANAGEMENT SYSTEM DEPLOYED!")
    print("=" * 80)
    
    print("\n📱 **Admin Commands:**")
    print("  • /sessions - Access session management (admin only)")
    print("  • /admin - Admin control panel")
    print("  • /status - System status")
    
    print("\n🤖 **Session Management Features:**")
    print("  • ➕ Add Session via Phone Number")
    print("  • 📄 Import Session String")
    print("  • 📱 QR Code Authentication")
    print("  • 📊 View All Sessions")
    print("  • 🗑️ Remove Sessions")
    print("  • ⚙️ Auto-Scale Settings")
    
    print("\n🔐 **Security Features:**")
    print("  • Admin-only access control")
    print("  • Encrypted session storage")
    print("  • Audit trail for all actions")
    print("  • Rate limiting and flood protection")
    
    print("\n📊 **Auto-Scaling:**")
    print("  • Automatic concurrent task adjustment")
    print("  • Load balancing across sessions")
    print("  • Health monitoring and recovery")
    print("  • Performance optimization")
    
    print("\n🔧 **Management Commands:**")
    print("  docker-compose logs -f          # View live logs")
    print("  docker-compose ps               # Check service status")
    print("  docker-compose restart          # Restart services")
    print("  docker-compose down             # Stop services")
    
    print("\n📈 **Scaling Recommendations:**")
    print("  • 10K daily reactions: 30 sessions, 50 concurrent tasks")
    print("  • 100K daily reactions: 100 sessions, 200 concurrent tasks")
    print("  • 1M+ daily reactions: 200 sessions, 500 concurrent tasks")
    
    print("\n⚠️  **Important Notes:**")
    print("  • Only users in ADMIN_USER_IDS can manage sessions")
    print("  • Sessions are encrypted and stored in MongoDB")
    print("  • System auto-scales based on active session count")
    print("  • Health monitoring runs every 5 minutes")
    
    print("\n" + "=" * 80)

def main():
    """Main deployment function"""
    print_banner()
    
    # Check prerequisites
    if not check_prerequisites():
        print("\n❌ Prerequisites check failed. Please install missing components.")
        sys.exit(1)
    
    # Validate environment
    if not validate_environment():
        print("\n❌ Environment validation failed. Please check your configuration.")
        sys.exit(1)
    
    # Build system
    if not build_system():
        print("\n❌ Build failed. Please check the error messages above.")
        sys.exit(1)
    
    # Deploy system
    if not deploy_system():
        print("\n❌ Deployment failed. Please check the error messages above.")
        sys.exit(1)
    
    # Verify deployment
    if not verify_deployment():
        print("\n⚠️  Deployment verification had issues. Please check manually.")
    
    # Show usage instructions
    show_usage_instructions()
    
    print("\n🎯 **Next Steps:**")
    print("1. Send /sessions to your bot to start adding userbot sessions")
    print("2. Add userbot sessions using phone numbers or session strings")
    print("3. Monitor system performance with /admin")
    print("4. Scale up sessions as needed for higher volumes")
    
    print("\n✅ Enhanced Session Management System is ready for production use!")

if __name__ == "__main__":
    main()
