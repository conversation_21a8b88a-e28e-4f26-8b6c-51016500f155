# =============================================================================
# TELEGRAM CONFIGURATION
# =============================================================================

# Main bot token from @BotFather
TELEGRAM_BOT_TOKEN=**********************************************

# Telegram API credentials from https://my.telegram.org
TELEGRAM_API_ID=24898981
TELEGRAM_API_HASH=ba03f24943472df711de9dd93f95f1d6

# Webhook configuration
WEBHOOK_URL=https://yourdomain.com/webhook
WEBHOOK_SECRET=your_webhook_secret_key_here

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# MongoDB Atlas connection string
MONGODB_URL=mongodb+srv://rehul123:<EMAIL>/telegram_bot?retryWrites=true&w=majority

# Database name
DATABASE_NAME=telegram_reaction_bot

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis connection for Celery and caching
REDIS_URL=redis://localhost:6379/0

# Redis database numbers for different purposes
REDIS_CELERY_DB=0
REDIS_CACHE_DB=1

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# 32-byte encryption key for session files (generate with: python -c "import secrets; print(secrets.token_hex(32))")
ENCRYPTION_KEY=ce92599814671168f871ecbb681e8d6c075a11c4d318dd87b9990db22c5abd2b

# JWT secret for API authentication
JWT_SECRET=e7b2a1c5d8f3b6a9e0c1f2d3b4a5e6f7c8d9a0b1c2d3e4f5a6b7c8d9e0f1a2b3c4d5e6f7a8b9c0d1e2f3a4b5c6d7e8

# Session file encryption password
SESSION_PASSWORD=YWp3RlBqQ2pXb1R4V1hJZ1B2Q0tyaW9l

# =============================================================================
# ADMIN CONFIGURATION
# =============================================================================

# Comma-separated list of admin user IDs
ADMIN_USER_IDS=1049516929,987654321
ADMIN_API_TOKEN=itR9eCxJuc4NbxdTJDjaThpEuWrI4UQ9kQB1TjAGJCs

# Admin notification chat ID (for alerts)
ADMIN_CHAT_ID=-1002858272842

# =============================================================================
# USERBOT CONFIGURATION
# =============================================================================

# Number of userbot sessions to run
USERBOT_COUNT=30

# Proxy configuration (optional)
USE_PROXY=false
PROXY_TYPE=http
PROXY_HOST=proxy.example.com
PROXY_PORT=8080
PROXY_USERNAME=proxy_user
PROXY_PASSWORD=proxy_pass

# Rate limiting
MIN_REACTION_DELAY=0.5
MAX_REACTION_DELAY=1.5

# =============================================================================
# API CONFIGURATION
# =============================================================================

# FastAPI server configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4

# CORS settings
CORS_ORIGINS=["http://localhost:3000", "https://yourdomain.com"]

# =============================================================================
# CELERY CONFIGURATION
# =============================================================================

# Celery worker configuration
CELERY_WORKER_CONCURRENCY=10
CELERY_TASK_SOFT_TIME_LIMIT=300
CELERY_TASK_TIME_LIMIT=600

# Task retry configuration
MAX_RETRIES=3
RETRY_DELAY=60

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Log file path
LOG_FILE=logs/app.log

# Enable structured logging
STRUCTURED_LOGGING=true

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================

# Prometheus metrics
ENABLE_METRICS=true
METRICS_PORT=9090

# Health check configuration
HEALTH_CHECK_INTERVAL=60

# Alert thresholds
ALERT_FAILURE_RATE_THRESHOLD=0.05
ALERT_QUEUE_SIZE_THRESHOLD=1000
ALERT_OFFLINE_SESSIONS_THRESHOLD=3

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Environment mode
ENVIRONMENT=development

# Debug mode
DEBUG=true

# Auto-reload for development
AUTO_RELOAD=true

# Test database (for running tests)
TEST_DATABASE_NAME=telegram_bot_test
