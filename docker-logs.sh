#!/bin/bash
# Docker Logs Script for Credit-Based Multi-Channel Auto-Reaction Bot

set -e

echo "📋 Auto-Reaction Bot Logs"
echo "========================="

# Check if docker-compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ docker-compose is not installed."
    exit 1
fi

# Function to show service logs
show_logs() {
    local service=$1
    local lines=${2:-50}
    
    echo ""
    echo "📋 $service logs (last $lines lines):"
    echo "----------------------------------------"
    docker-compose logs --tail=$lines $service
}

# Parse command line arguments
SERVICE=${1:-"all"}
LINES=${2:-50}
FOLLOW=${3:-false}

case $SERVICE in
    "all")
        if [ "$FOLLOW" = "follow" ] || [ "$FOLLOW" = "-f" ]; then
            echo "📋 Following all service logs (Ctrl+C to stop):"
            docker-compose logs -f
        else
            echo "📋 All service logs (last $LINES lines each):"
            docker-compose logs --tail=$LINES
        fi
        ;;
    "bot")
        if [ "$FOLLOW" = "follow" ] || [ "$FOLLOW" = "-f" ]; then
            docker-compose logs -f bot
        else
            show_logs "bot" $LINES
        fi
        ;;
    "worker")
        if [ "$FOLLOW" = "follow" ] || [ "$FOLLOW" = "-f" ]; then
            docker-compose logs -f worker
        else
            show_logs "worker" $LINES
        fi
        ;;
    "api")
        if [ "$FOLLOW" = "follow" ] || [ "$FOLLOW" = "-f" ]; then
            docker-compose logs -f api
        else
            show_logs "api" $LINES
        fi
        ;;
    "mongodb")
        if [ "$FOLLOW" = "follow" ] || [ "$FOLLOW" = "-f" ]; then
            docker-compose logs -f mongodb
        else
            show_logs "mongodb" $LINES
        fi
        ;;
    "redis")
        if [ "$FOLLOW" = "follow" ] || [ "$FOLLOW" = "-f" ]; then
            docker-compose logs -f redis
        else
            show_logs "redis" $LINES
        fi
        ;;
    "scheduler")
        if [ "$FOLLOW" = "follow" ] || [ "$FOLLOW" = "-f" ]; then
            docker-compose logs -f scheduler
        else
            show_logs "scheduler" $LINES
        fi
        ;;
    "flower")
        if [ "$FOLLOW" = "follow" ] || [ "$FOLLOW" = "-f" ]; then
            docker-compose logs -f flower
        else
            show_logs "flower" $LINES
        fi
        ;;
    *)
        echo "❌ Unknown service: $SERVICE"
        echo ""
        echo "📋 Available services:"
        echo "   - all (default)"
        echo "   - bot"
        echo "   - worker"
        echo "   - api"
        echo "   - mongodb"
        echo "   - redis"
        echo "   - scheduler"
        echo "   - flower"
        echo ""
        echo "📋 Usage examples:"
        echo "   ./docker-logs.sh                    # All services, last 50 lines"
        echo "   ./docker-logs.sh bot                # Bot service, last 50 lines"
        echo "   ./docker-logs.sh bot 100            # Bot service, last 100 lines"
        echo "   ./docker-logs.sh bot 50 follow      # Bot service, follow mode"
        echo "   ./docker-logs.sh all 50 -f          # All services, follow mode"
        exit 1
        ;;
esac

if [ "$FOLLOW" != "follow" ] && [ "$FOLLOW" != "-f" ]; then
    echo ""
    echo "💡 To follow logs in real-time, add 'follow' or '-f' as the last parameter"
    echo "💡 Example: ./docker-logs.sh bot 50 follow"
fi
