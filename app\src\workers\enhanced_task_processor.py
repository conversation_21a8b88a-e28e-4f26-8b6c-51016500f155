#!/usr/bin/env python3
"""
Enhanced Task Processor - Phase 3 Implementation
"""

import asyncio
import random
import os
from datetime import datetime, timezone, timedelta
from typing import Optional, Dict, Any, List
from motor.motor_asyncio import AsyncIOMotorClient
from bson import ObjectId
import logging

# Add src to path for imports
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from services.credit_service import CreditService
from services.userbot_manager import UserbotManager
from services.reaction_logger import reaction_logger

logger = logging.getLogger(__name__)

class EnhancedTaskProcessor:
    """Enhanced task processor with intelligent scheduling and retry logic"""
    
    def __init__(self, db_client: AsyncIOMotorClient):
        self.db = db_client.telegram_reaction_bot
        self.credit_service = CreditService(db_client)
        self.userbot_manager = UserbotManager(db_client)
        self.processing = False
        self.max_concurrent_tasks = 50
        self.processing_semaphore = asyncio.Semaphore(self.max_concurrent_tasks)
        
    async def start_processing(self):
        """Start the task processing loop"""
        
        logger.info("Starting enhanced task processor...")
        
        # Load userbot sessions
        loaded_sessions = await self.userbot_manager.load_all_sessions()
        logger.info(f"Loaded {loaded_sessions} userbot sessions")
        
        if loaded_sessions == 0:
            logger.warning("No userbot sessions loaded - reactions will not be sent")
        
        # Start health monitoring
        await self.userbot_manager.start_health_monitoring()
        await self.userbot_manager.start_daily_reset_scheduler()
        
        self.processing = True
        
        # Start processing tasks
        await asyncio.gather(
            self.process_scheduled_tasks(),
            self.process_retry_tasks(),
            self.cleanup_old_tasks(),
            self.monitor_system_health()
        )
    
    async def process_scheduled_tasks(self):
        """Process tasks that are scheduled to run now"""
        
        while self.processing:
            try:
                now = datetime.now(timezone.utc)
                
                # Get tasks ready for processing
                tasks = await self.db.reaction_tasks.find({
                    "status": "pending",
                    "scheduled_at": {"$lte": now}
                }).sort("scheduled_at", 1).limit(100).to_list(length=100)
                
                if tasks:
                    logger.info(f"Processing {len(tasks)} scheduled tasks")
                    
                    # Process tasks concurrently
                    await asyncio.gather(
                        *[self.process_single_task(task) for task in tasks],
                        return_exceptions=True
                    )
                
                # Wait before next iteration
                await asyncio.sleep(5)
                
            except Exception as e:
                logger.error(f"Error in scheduled task processing: {e}")
                await asyncio.sleep(10)
    
    async def process_retry_tasks(self):
        """Process tasks that need to be retried"""
        
        while self.processing:
            try:
                # Wait longer for retry processing
                await asyncio.sleep(30)
                
                now = datetime.now(timezone.utc)
                
                # Get failed tasks that can be retried
                retry_tasks = await self.db.reaction_tasks.find({
                    "status": "failed",
                    "attempts": {"$lt": "$max_attempts"},
                    "scheduled_at": {"$lte": now}
                }).limit(20).to_list(length=20)
                
                if retry_tasks:
                    logger.info(f"Retrying {len(retry_tasks)} failed tasks")
                    
                    for task in retry_tasks:
                        # Reset task for retry
                        await self.db.reaction_tasks.update_one(
                            {"_id": task["_id"]},
                            {
                                "$set": {
                                    "status": "pending",
                                    "scheduled_at": datetime.now(timezone.utc) + timedelta(seconds=random.randint(10, 60))
                                }
                            }
                        )
                
            except Exception as e:
                logger.error(f"Error in retry task processing: {e}")
                await asyncio.sleep(60)
    
    async def process_single_task(self, task: Dict[str, Any]) -> bool:
        """Process a single reaction task with concurrency control"""
        
        async with self.processing_semaphore:
            return await self._process_task_internal(task)
    
    async def _process_task_internal(self, task: Dict[str, Any]) -> bool:
        """Internal task processing logic"""
        
        task_id = str(task["_id"])
        user_id = task["telegram_user_id"]
        channel_id = task["channel_id"]
        message_id = task["message_id"]
        emoji = task["emoji"]
        
        try:
            # Mark task as processing
            await self.db.reaction_tasks.update_one(
                {"_id": task["_id"]},
                {
                    "$set": {
                        "status": "processing",
                        "started_at": datetime.now(timezone.utc)
                    }
                }
            )
            
            # Check user's credit balance before processing (but don't consume yet)
            user_balance = await self.credit_service.get_user_balance(user_id)
            if user_balance < task["credits_consumed"]:
                # Insufficient credits - mark task as failed
                await self.db.reaction_tasks.update_one(
                    {"_id": task["_id"]},
                    {
                        "$set": {
                            "status": "failed",
                            "error_message": f"Insufficient credits. Balance: {user_balance}, Required: {task['credits_consumed']}",
                            "completed_at": datetime.now(timezone.utc)
                        }
                    }
                )
                logger.warning(f"Task {task_id} failed: Insufficient credits")
                return False
            
            # Get best session for sending reaction
            excluded_sessions = []
            if task.get("assigned_session"):
                # If task failed before, exclude the previously assigned session
                if task.get("attempts", 0) > 0:
                    excluded_sessions = [task["assigned_session"]]
            
            session_name = await self.userbot_manager.get_best_session(excluded_sessions)
            
            if not session_name:
                # No available sessions - reschedule task
                await self.reschedule_task(task, "No available sessions")
                return False
            
            # Send reaction
            reaction_success, error_message = await self.userbot_manager.send_reaction_with_session(
                session_name, channel_id, message_id, emoji
            )
            
            if reaction_success:
                # Consume credits ONLY after successful reaction
                if not task.get("credit_transaction_id"):
                    success, transaction_id = await self.credit_service.consume_credits(
                        user_id,
                        task["credits_consumed"],
                        f"Reaction {emoji} to message {message_id}",
                        reference_id=task_id
                    )

                    if not success:
                        logger.error(f"Failed to consume credits after successful reaction for task {task_id}: {transaction_id}")
                        # Still mark as completed since reaction was sent, but log the credit issue
                    else:
                        # Update task with transaction ID
                        await self.db.reaction_tasks.update_one(
                            {"_id": task["_id"]},
                            {"$set": {"credit_transaction_id": transaction_id}}
                        )

                # Mark task as completed
                await self.db.reaction_tasks.update_one(
                    {"_id": task["_id"]},
                    {
                        "$set": {
                            "status": "completed",
                            "assigned_session": session_name,
                            "completed_at": datetime.now(timezone.utc)
                        }
                    }
                )

                # Update channel statistics
                await self.update_channel_stats(task["channel_doc_id"], success=True)

                # Log successful reaction
                channel_doc = await self.db.channels.find_one({"channel_id": task["channel_id"]})
                channel_name = channel_doc.get("channel_title", "Unknown") if channel_doc else "Unknown"

                await reaction_logger.log_reaction_success(
                    channel_name=channel_name,
                    channel_id=task["channel_id"],
                    post_id=task["message_id"],
                    emoji=task["emoji"],
                    user_id=task["telegram_user_id"],
                    session_name=session_name
                )

                logger.info(f"Task {task_id} completed successfully by {session_name}")
                return True
            else:
                # Log failed reaction
                channel_doc = await self.db.channels.find_one({"channel_id": task["channel_id"]})
                channel_name = channel_doc.get("channel_title", "Unknown") if channel_doc else "Unknown"

                await reaction_logger.log_reaction_failure(
                    channel_name=channel_name,
                    channel_id=task["channel_id"],
                    post_id=task["message_id"],
                    emoji=task["emoji"],
                    user_id=task["telegram_user_id"],
                    error_message=error_message,
                    session_name=session_name
                )

                # Reaction failed - handle retry logic
                await self.handle_reaction_failure(task, session_name, error_message)
                return False
                
        except Exception as e:
            logger.error(f"Error processing task {task_id}: {e}")
            await self.handle_task_error(task, str(e))
            return False
    
    async def handle_reaction_failure(
        self, 
        task: Dict[str, Any], 
        session_name: str, 
        error_message: str
    ):
        """Handle reaction failure with intelligent retry logic"""
        
        attempts = task.get("attempts", 0) + 1
        max_attempts = task.get("max_attempts", 3)
        
        if attempts >= max_attempts:
            # Max attempts reached - mark as failed and refund credits
            await self.db.reaction_tasks.update_one(
                {"_id": task["_id"]},
                {
                    "$set": {
                        "status": "failed",
                        "attempts": attempts,
                        "error_message": error_message,
                        "completed_at": datetime.now(timezone.utc)
                    }
                }
            )
            
            # No need to refund credits since they are only consumed after successful reactions
            
            # Update channel statistics
            await self.update_channel_stats(task["channel_doc_id"], success=False)
            
            logger.warning(f"Task {task['_id']} failed permanently after {attempts} attempts")
            
        else:
            # Retry with exponential backoff
            retry_delay = min(300, 30 * (2 ** (attempts - 1)))  # Max 5 minutes
            retry_time = datetime.now(timezone.utc) + timedelta(seconds=retry_delay)
            
            await self.db.reaction_tasks.update_one(
                {"_id": task["_id"]},
                {
                    "$set": {
                        "status": "pending",
                        "attempts": attempts,
                        "scheduled_at": retry_time,
                        "error_message": error_message,
                        "assigned_session": None  # Try different session
                    }
                }
            )
            
            logger.info(f"Task {task['_id']} scheduled for retry {attempts}/{max_attempts} in {retry_delay}s")
    
    async def reschedule_task(self, task: Dict[str, Any], reason: str):
        """Reschedule task for later processing"""
        
        retry_delay = random.randint(60, 300)  # 1-5 minutes
        retry_time = datetime.now(timezone.utc) + timedelta(seconds=retry_delay)
        
        await self.db.reaction_tasks.update_one(
            {"_id": task["_id"]},
            {
                "$set": {
                    "status": "pending",
                    "scheduled_at": retry_time,
                    "error_message": reason
                }
            }
        )
        
        logger.info(f"Task {task['_id']} rescheduled: {reason}")
    
    async def handle_task_error(self, task: Dict[str, Any], error: str):
        """Handle unexpected task processing errors"""
        
        await self.db.reaction_tasks.update_one(
            {"_id": task["_id"]},
            {
                "$set": {
                    "status": "failed",
                    "error_message": error,
                    "completed_at": datetime.now(timezone.utc)
                },
                "$inc": {"attempts": 1}
            }
        )
        
        logger.error(f"Task {task['_id']} failed with error: {error}")
    
    async def update_channel_stats(self, channel_doc_id: str, success: bool):
        """Update channel statistics"""
        
        try:
            update_data = {
                "updated_at": datetime.now(timezone.utc),
                "last_post_processed": datetime.now(timezone.utc)
            }
            
            if success:
                update_data["$inc"] = {
                    "total_reactions_sent": 1,
                    "total_credits_consumed": 1
                }
            
            await self.db.channels.update_one(
                {"_id": ObjectId(channel_doc_id)},
                update_data
            )
        except Exception as e:
            logger.error(f"Error updating channel stats: {e}")
    
    async def cleanup_old_tasks(self):
        """Clean up old completed/failed tasks"""
        
        while self.processing:
            try:
                await asyncio.sleep(3600)  # Run every hour
                
                # Remove tasks older than 7 days
                cutoff_date = datetime.now(timezone.utc) - timedelta(days=7)
                
                result = await self.db.reaction_tasks.delete_many({
                    "status": {"$in": ["completed", "failed", "cancelled"]},
                    "completed_at": {"$lt": cutoff_date}
                })
                
                if result.deleted_count > 0:
                    logger.info(f"Cleaned up {result.deleted_count} old tasks")
                
            except Exception as e:
                logger.error(f"Error in cleanup: {e}")
    
    async def monitor_system_health(self):
        """Monitor system health and performance"""
        
        while self.processing:
            try:
                await asyncio.sleep(300)  # Check every 5 minutes
                
                # Get system statistics
                stats = await self.userbot_manager.get_session_statistics()
                
                # Check for issues
                if stats["healthy_sessions"] == 0:
                    logger.critical("No healthy userbot sessions available!")
                elif stats["healthy_sessions"] < 3:
                    logger.warning(f"Only {stats['healthy_sessions']} healthy sessions available")
                
                # Check pending task queue
                pending_tasks = await self.db.reaction_tasks.count_documents({"status": "pending"})
                if pending_tasks > 1000:
                    logger.warning(f"High pending task queue: {pending_tasks} tasks")
                
                logger.info(f"System health: {stats['healthy_sessions']}/{stats['total_sessions']} sessions, {pending_tasks} pending tasks")
                
            except Exception as e:
                logger.error(f"Error in health monitoring: {e}")
    
    async def stop_processing(self):
        """Stop the task processor"""
        
        logger.info("Stopping task processor...")
        self.processing = False
        
        # Shutdown userbot sessions
        await self.userbot_manager.shutdown_all_sessions()
        
        logger.info("Task processor stopped")
