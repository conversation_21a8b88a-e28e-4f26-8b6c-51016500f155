# Telegram Auto-Reaction Bot - Linux VPS Deployment Guide

This guide provides step-by-step instructions for deploying the Telegram Auto-Reaction Bot on a Linux VPS (Ubuntu/CentOS) for production use.

## 🖥️ VPS Requirements

### Minimum Specifications
- **OS**: Ubuntu 20.04+ or CentOS 8+
- **CPU**: 1 vCPU (2+ recommended)
- **RAM**: 1GB (2GB+ recommended)
- **Storage**: 10GB SSD
- **Network**: 1Gbps connection

### Recommended Specifications
- **OS**: Ubuntu 22.04 LTS
- **CPU**: 2+ vCPUs
- **RAM**: 4GB+
- **Storage**: 20GB+ SSD
- **Network**: High-speed connection with low latency

## 🚀 Initial Server Setup

### 1. Connect to Your VPS
```bash
ssh root@your-server-ip
# or
ssh your-username@your-server-ip
```

### 2. Update System Packages

**Ubuntu/Debian:**
```bash
apt update && apt upgrade -y
apt install -y curl wget git nano htop unzip
```

**CentOS/RHEL:**
```bash
yum update -y
yum install -y curl wget git nano htop unzip epel-release
```

### 3. Create Application User
```bash
# Create dedicated user for the application
useradd -m -s /bin/bash telegrambot
usermod -aG sudo telegrambot

# Switch to application user
su - telegrambot
```

### 4. Configure Firewall

**Ubuntu (UFW):**
```bash
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 8000/tcp  # FastAPI server
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw status
```

**CentOS (firewalld):**
```bash
sudo systemctl enable firewalld
sudo systemctl start firewalld
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-port=8000/tcp
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

## 🐳 Docker Installation

### 1. Install Docker

**Ubuntu:**
```bash
# Remove old versions
sudo apt remove docker docker-engine docker.io containerd runc

# Install dependencies
sudo apt update
sudo apt install -y apt-transport-https ca-certificates curl gnupg lsb-release

# Add Docker GPG key
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# Add Docker repository
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# Install Docker
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io
```

**CentOS:**
```bash
# Remove old versions
sudo yum remove docker docker-client docker-client-latest docker-common docker-latest docker-latest-logrotate docker-logrotate docker-engine

# Install dependencies
sudo yum install -y yum-utils

# Add Docker repository
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo

# Install Docker
sudo yum install -y docker-ce docker-ce-cli containerd.io
```

### 2. Install Docker Compose
```bash
# Download Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

# Make executable
sudo chmod +x /usr/local/bin/docker-compose

# Verify installation
docker-compose --version
```

### 3. Configure Docker
```bash
# Start and enable Docker
sudo systemctl start docker
sudo systemctl enable docker

# Add user to docker group
sudo usermod -aG docker $USER

# Logout and login again for group changes to take effect
exit
ssh telegrambot@your-server-ip

# Verify Docker installation
docker --version
docker-compose --version
```

## 🛠️ Python Installation

### 1. Install Python 3.8+

**Ubuntu:**
```bash
sudo apt update
sudo apt install -y python3 python3-pip python3-venv python3-dev
```

**CentOS:**
```bash
sudo yum install -y python3 python3-pip python3-devel
```

### 2. Verify Python Installation
```bash
python3 --version
pip3 --version
```

## 📦 Project Deployment

### 1. Clone Repository
```bash
cd /home/<USER>
git clone <your-repository-url> telegram-auto-reaction-bot
cd telegram-auto-reaction-bot
```

### 2. Create Python Virtual Environment
```bash
python3 -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
```

### 3. Create Production Environment File
```bash
nano .env
```

Add the following configuration:
```env
# Telegram Configuration
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_API_ID=your_api_id
TELEGRAM_API_HASH=your_api_hash

# Database Configuration
MONGODB_URL=mongodb://localhost:27017/telegram_reaction_bot

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Admin Configuration
ADMIN_USER_IDS=your_telegram_user_id

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000

# Production Settings
ENVIRONMENT=production
LOG_LEVEL=INFO
```

### 4. Set File Permissions
```bash
chmod 600 .env
chmod +x *.py
```

### 5. Start Docker Services
```bash
docker-compose up -d mongodb redis

# Verify services are running
docker ps
docker-compose logs mongodb
docker-compose logs redis
```

### 6. Create Userbot Session
```bash
source venv/bin/activate
python3 -c "
from pyrogram import Client
import os
from dotenv import load_dotenv
load_dotenv()

client = Client(
    'sessions/userbot',
    api_id=int(os.getenv('TELEGRAM_API_ID')),
    api_hash=os.getenv('TELEGRAM_API_HASH')
)
print('Starting session creation...')
client.start()
print('Session created successfully!')
client.stop()
"
```

## 🔧 Process Management with Systemd

### 1. Create Systemd Service Files

**FastAPI Server Service:**
```bash
sudo nano /etc/systemd/system/telegram-bot-api.service
```

```ini
[Unit]
Description=Telegram Auto-Reaction Bot API Server
After=network.target docker.service
Requires=docker.service

[Service]
Type=simple
User=telegrambot
Group=telegrambot
WorkingDirectory=/home/<USER>/telegram-auto-reaction-bot
Environment=PATH=/home/<USER>/telegram-auto-reaction-bot/venv/bin
ExecStart=/home/<USER>/telegram-auto-reaction-bot/venv/bin/python launch_api.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

**Enhanced Bot Service:**
```bash
sudo nano /etc/systemd/system/telegram-bot-enhanced.service
```

```ini
[Unit]
Description=Telegram Auto-Reaction Enhanced Bot
After=network.target telegram-bot-api.service
Requires=telegram-bot-api.service

[Service]
Type=simple
User=telegrambot
Group=telegrambot
WorkingDirectory=/home/<USER>/telegram-auto-reaction-bot
Environment=PATH=/home/<USER>/telegram-auto-reaction-bot/venv/bin
ExecStart=/home/<USER>/telegram-auto-reaction-bot/venv/bin/python enhanced_bot.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

**Userbot Worker Service:**
```bash
sudo nano /etc/systemd/system/telegram-bot-userbot.service
```

```ini
[Unit]
Description=Telegram Auto-Reaction Userbot Worker
After=network.target telegram-bot-api.service
Requires=telegram-bot-api.service

[Service]
Type=simple
User=telegrambot
Group=telegrambot
WorkingDirectory=/home/<USER>/telegram-auto-reaction-bot
Environment=PATH=/home/<USER>/telegram-auto-reaction-bot/venv/bin
ExecStart=/home/<USER>/telegram-auto-reaction-bot/venv/bin/python fixed_userbot_worker.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

**Celery Worker Service:**
```bash
sudo nano /etc/systemd/system/telegram-bot-celery.service
```

```ini
[Unit]
Description=Telegram Auto-Reaction Celery Worker
After=network.target telegram-bot-api.service
Requires=telegram-bot-api.service

[Service]
Type=simple
User=telegrambot
Group=telegrambot
WorkingDirectory=/home/<USER>/telegram-auto-reaction-bot
Environment=PATH=/home/<USER>/telegram-auto-reaction-bot/venv/bin
ExecStart=/home/<USER>/telegram-auto-reaction-bot/venv/bin/python launch_celery.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### 2. Enable and Start Services
```bash
# Reload systemd
sudo systemctl daemon-reload

# Enable services to start on boot
sudo systemctl enable telegram-bot-api
sudo systemctl enable telegram-bot-enhanced
sudo systemctl enable telegram-bot-userbot
sudo systemctl enable telegram-bot-celery

# Start all services
sudo systemctl start telegram-bot-api
sudo systemctl start telegram-bot-enhanced
sudo systemctl start telegram-bot-userbot
sudo systemctl start telegram-bot-celery

# Check service status
sudo systemctl status telegram-bot-api
sudo systemctl status telegram-bot-enhanced
sudo systemctl status telegram-bot-userbot
sudo systemctl status telegram-bot-celery
```

### 3. Service Management Commands
```bash
# View logs
sudo journalctl -u telegram-bot-api -f
sudo journalctl -u telegram-bot-enhanced -f
sudo journalctl -u telegram-bot-userbot -f
sudo journalctl -u telegram-bot-celery -f

# Restart services
sudo systemctl restart telegram-bot-api
sudo systemctl restart telegram-bot-enhanced
sudo systemctl restart telegram-bot-userbot
sudo systemctl restart telegram-bot-celery

# Stop services
sudo systemctl stop telegram-bot-api
sudo systemctl stop telegram-bot-enhanced
sudo systemctl stop telegram-bot-userbot
sudo systemctl stop telegram-bot-celery
```

## 🔒 SSL/HTTPS Setup with Nginx

### 1. Install Nginx
**Ubuntu:**
```bash
sudo apt install -y nginx
```

**CentOS:**
```bash
sudo yum install -y nginx
```

### 2. Configure Nginx
```bash
sudo nano /etc/nginx/sites-available/telegram-bot
```

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 3. Enable Nginx Configuration
```bash
# Ubuntu
sudo ln -s /etc/nginx/sites-available/telegram-bot /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx

# CentOS
sudo cp /etc/nginx/sites-available/telegram-bot /etc/nginx/conf.d/telegram-bot.conf
sudo nginx -t
sudo systemctl restart nginx
```

### 4. Install SSL Certificate with Let's Encrypt
```bash
# Install Certbot
sudo apt install -y certbot python3-certbot-nginx  # Ubuntu
sudo yum install -y certbot python3-certbot-nginx  # CentOS

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Test automatic renewal
sudo certbot renew --dry-run
```

## 📊 Monitoring and Logging

### 1. System Monitoring
```bash
# Install monitoring tools
sudo apt install -y htop iotop nethogs  # Ubuntu
sudo yum install -y htop iotop nethogs  # CentOS

# Monitor system resources
htop
iotop
nethogs
```

### 2. Application Monitoring
```bash
# Check application health
curl http://localhost:8000/health
curl http://localhost:8000/admin/stats

# Monitor Docker containers
docker stats
docker-compose logs -f mongodb
docker-compose logs -f redis
```

### 3. Log Management
```bash
# Create log rotation configuration
sudo nano /etc/logrotate.d/telegram-bot
```

```
/var/log/telegram-bot/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 telegrambot telegrambot
    postrotate
        systemctl reload telegram-bot-*
    endscript
}
```

## 🔐 Security Best Practices

### 1. SSH Security
```bash
# Disable root login and password authentication
sudo nano /etc/ssh/sshd_config
```

```
PermitRootLogin no
PasswordAuthentication no
PubkeyAuthentication yes
```

```bash
sudo systemctl restart sshd
```

### 2. Fail2Ban Installation
```bash
sudo apt install -y fail2ban  # Ubuntu
sudo yum install -y fail2ban  # CentOS

sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

### 3. Regular Updates
```bash
# Create update script
nano ~/update-system.sh
```

```bash
#!/bin/bash
sudo apt update && sudo apt upgrade -y  # Ubuntu
# sudo yum update -y  # CentOS

# Update Docker images
cd /home/<USER>/telegram-auto-reaction-bot
docker-compose pull
docker-compose up -d

# Restart services
sudo systemctl restart telegram-bot-*
```

```bash
chmod +x ~/update-system.sh

# Add to crontab for weekly updates
crontab -e
# Add: 0 2 * * 0 /home/<USER>/update-system.sh
```

## 💾 Backup and Maintenance

### 1. Database Backup
```bash
# Create backup script
nano ~/backup-database.sh
```

```bash
#!/bin/bash
BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# Backup MongoDB
docker exec telegram_bot_mongodb mongodump --out /tmp/backup
docker cp telegram_bot_mongodb:/tmp/backup $BACKUP_DIR/mongodb_$DATE

# Backup sessions
cp -r /home/<USER>/telegram-auto-reaction-bot/sessions $BACKUP_DIR/sessions_$DATE

# Cleanup old backups (keep last 7 days)
find $BACKUP_DIR -type d -mtime +7 -exec rm -rf {} \;
```

```bash
chmod +x ~/backup-database.sh

# Add to crontab for daily backups
crontab -e
# Add: 0 3 * * * /home/<USER>/backup-database.sh
```

### 2. System Maintenance
```bash
# Create maintenance script
nano ~/maintenance.sh
```

```bash
#!/bin/bash
# Clean Docker
docker system prune -f

# Clean logs
sudo journalctl --vacuum-time=7d

# Check disk space
df -h

# Check service status
systemctl status telegram-bot-*
```

## 🐛 Common Deployment Issues

### 1. Permission Denied Errors
```bash
# Fix file permissions
sudo chown -R telegrambot:telegrambot /home/<USER>/telegram-auto-reaction-bot
chmod +x /home/<USER>/telegram-auto-reaction-bot/*.py
```

### 2. Docker Service Not Starting
```bash
# Check Docker status
sudo systemctl status docker
sudo systemctl restart docker

# Check Docker Compose
cd /home/<USER>/telegram-auto-reaction-bot
docker-compose down
docker-compose up -d
```

### 3. Port Already in Use
```bash
# Find process using port
sudo netstat -tulpn | grep :8000
sudo lsof -i :8000

# Kill process if needed
sudo kill -9 <PID>
```

### 4. Service Won't Start
```bash
# Check service logs
sudo journalctl -u telegram-bot-api -n 50
sudo journalctl -u telegram-bot-enhanced -n 50

# Check Python environment
source /home/<USER>/telegram-auto-reaction-bot/venv/bin/activate
python3 -c "import pyrogram; print('Pyrogram OK')"
```

### 5. Database Connection Issues
```bash
# Check MongoDB container
docker logs telegram_bot_mongodb

# Test MongoDB connection
docker exec telegram_bot_mongodb mongosh --eval "db.adminCommand('ping')"

# Check Redis connection
docker exec telegram_bot_redis redis-cli ping
```

## ✅ Deployment Verification

### 1. Health Checks
```bash
# API health check
curl http://localhost:8000/health

# System statistics
curl http://localhost:8000/admin/stats

# Check all services
sudo systemctl status telegram-bot-*
```

### 2. Test Bot Functionality
1. Message @multi_reactions_bot on Telegram
2. Send `/start` command
3. Forward a channel message
4. Test reaction functionality

### 3. Monitor Logs
```bash
# Real-time log monitoring
sudo journalctl -f -u telegram-bot-api
sudo journalctl -f -u telegram-bot-enhanced
sudo journalctl -f -u telegram-bot-userbot
sudo journalctl -f -u telegram-bot-celery
```

---

## 🎉 Deployment Complete!

Your Telegram Auto-Reaction Bot is now deployed and running in production on your Linux VPS. The system is configured with:

- ✅ **Automated startup** with systemd services
- ✅ **SSL/HTTPS** with Let's Encrypt
- ✅ **Process monitoring** and automatic restarts
- ✅ **Security hardening** with firewall and fail2ban
- ✅ **Automated backups** and maintenance
- ✅ **Comprehensive logging** and monitoring

**Your bot is ready for production use! 🚀**
