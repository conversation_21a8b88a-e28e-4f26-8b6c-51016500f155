#!/usr/bin/env python3
"""
Comprehensive UI/UX Handlers for Auto-Reaction Bot
Implements complete user interface with enhanced user experience
"""

import os
import sys
import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone, timedelta
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from motor.motor_asyncio import AsyncIOMotorClient

# Add src to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, '..', '..')
sys.path.insert(0, src_dir)

from services.credit_service import CreditService
from services.channel_service import ChannelService

logger = logging.getLogger(__name__)

class UIHandlers:
    """Comprehensive UI/UX handlers for enhanced user experience"""
    
    def __init__(self, db_client: AsyncIOMotorClient):
        self.db = db_client.telegram_reaction_bot
        self.credit_service = CreditService(db_client)
        self.channel_service = ChannelService(db_client)
    
    async def show_add_channel_flow(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Enhanced add channel flow with validation"""
        
        query = update.callback_query
        
        text = """
➕ **ADD YOUR CHANNEL**

**Step-by-step guide:**

**1️⃣ Make Bot Admin**
• Go to your channel settings
• Add this bot as an administrator
• Grant necessary permissions (post messages, read messages)

**2️⃣ Forward a Post**
• Forward any message from your channel to this bot
• I'll automatically detect and validate the channel

**3️⃣ Verification**
• Complete the verification process
• Configure your reaction settings

**📋 Requirements:**
✅ Channel must be public or bot must be admin
✅ Bot needs admin permissions in the channel
✅ Channel should have recent activity

**Ready to add your channel?**
Make me an admin first, then forward any post from your channel.
"""
        
        keyboard = [
            [InlineKeyboardButton("📖 Detailed Guide", callback_data="guide:add_channel")],
            [InlineKeyboardButton("🔙 Back to Menu", callback_data="menu:main")]
        ]
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
    
    async def show_my_channels(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Display user's channels with management options"""
        
        query = update.callback_query
        user_id = update.effective_user.id
        
        # Get user channels
        user_channels = await self.channel_service.get_user_channels(user_id)
        
        if not user_channels:
            text = """
📺 **MY CHANNELS**

You haven't added any channels yet.

**Get started:**
1. Click "➕ Add Channel" to add your first channel
2. Make the bot an admin in your channel
3. Forward a message from your channel
4. Complete verification and start automating!

**Benefits of adding channels:**
• Automatic reactions to all posts
• Customizable emoji sets
• Smart timing and randomization
• Detailed analytics and insights
"""
            
            keyboard = [
                [InlineKeyboardButton("➕ Add Your First Channel", callback_data="menu:add_channel")],
                [InlineKeyboardButton("🔙 Back to Menu", callback_data="menu:main")]
            ]
        else:
            text = f"""
📺 **MY CHANNELS** ({len(user_channels)} total)

"""
            
            keyboard = []
            
            # Escape Markdown special characters
            def escape_markdown(text):
                if not text:
                    return "N/A"
                # Escape Markdown special characters
                special_chars = ['*', '_', '`', '[', ']', '(', ')', '~', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!']
                for char in special_chars:
                    text = str(text).replace(char, f'\\{char}')
                return text

            for i, channel in enumerate(user_channels[:10]):  # Limit to 10 channels for UI
                status_emoji = "✅" if channel.get("is_active") else "⏸️"
                verification_emoji = "🔒" if channel.get("verification_status") == "verified" else "🔓"

                channel_title_raw = channel.get("channel_title", "Unknown Channel")
                channel_name = channel_title_raw[:25]
                if len(channel_title_raw) > 25:
                    channel_name += "..."

                # Escape for button text (no markdown needed)
                channel_name_safe = channel_name.replace('*', '').replace('_', '').replace('`', '')

                # Channel info button
                keyboard.append([
                    InlineKeyboardButton(
                        f"{status_emoji}{verification_emoji} {channel_name_safe}",
                        callback_data=f"channel:info:{channel['_id']}"
                    )
                ])

                # Escape for markdown text
                channel_title_escaped = escape_markdown(channel_title_raw)

                text += f"""
**{i+1}\\. {channel_title_escaped}**
Status: {status_emoji} {"Active" if channel.get("is_active") else "Inactive"}
Verification: {verification_emoji} {channel.get("verification_status", "pending").title()}
Reactions/Post: {channel.get("reactions_per_post", 1)}
"""
            
            # Add management buttons
            keyboard.extend([
                [InlineKeyboardButton("➕ Add Another Channel", callback_data="menu:add_channel")],
                [InlineKeyboardButton("🔙 Back to Menu", callback_data="menu:main")]
            ])
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
    
    async def show_channel_info(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show detailed channel information and management options"""
        
        query = update.callback_query
        channel_id = query.data.split(":")[-1]
        
        try:
            from bson import ObjectId
            channel = await self.db.channels.find_one({"_id": ObjectId(channel_id)})
            
            if not channel:
                await query.answer("Channel not found", show_alert=True)
                return
            
            # Get channel statistics
            today_start = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
            
            reactions_today = await self.db.reaction_tasks.count_documents({
                "channel_id": channel["channel_id"],
                "created_at": {"$gte": today_start}
            })
            
            total_reactions = await self.db.reaction_tasks.count_documents({
                "channel_id": channel["channel_id"]
            })
            
            # Escape Markdown special characters
            def escape_markdown(text):
                if not text:
                    return "N/A"
                # Escape Markdown special characters
                special_chars = ['*', '_', '`', '[', ']', '(', ')', '~', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!']
                for char in special_chars:
                    text = str(text).replace(char, f'\\{char}')
                return text

            channel_title = escape_markdown(channel.get('channel_title', 'Unknown Channel'))
            channel_username = escape_markdown(channel.get('channel_username', 'N/A'))

            text = f"""
📺 **{channel_title}**

**📊 Channel Information:**
🆔 **ID**: `{channel.get('channel_id')}`
📝 **Username**: @{channel_username}
🔒 **Status**: {channel.get('verification_status', 'pending').title()}
⚡ **Active**: {"Yes" if channel.get('is_active') else "No"}

**⚙️ Current Settings:**
🎭 **Reactions per Post**: {channel.get('reactions_per_post', 1)}
😀 **Emojis**: {', '.join(channel.get('emoji_list', ['👍']))}
🎲 **Mode**: {channel.get('reaction_mode', 'random').title()}
⏱️ **Delay**: {channel.get('reaction_delay_min', 5)}-{channel.get('reaction_delay_max', 30)}s

**📈 Statistics:**
🎭 **Today**: {reactions_today} reactions
📊 **Total**: {total_reactions} reactions
📅 **Added**: {channel.get('created_at', datetime.now()).strftime('%Y-%m-%d')}
"""
            
            keyboard = [
                [
                    InlineKeyboardButton("⚙️ Configure", callback_data=f"channel:config:{channel_id}"),
                    InlineKeyboardButton("📊 Statistics", callback_data=f"channel:stats:{channel_id}")
                ],
                [
                    InlineKeyboardButton("⏸️ Pause" if channel.get('is_active') else "▶️ Resume", 
                                       callback_data=f"channel:toggle:{channel_id}"),
                    InlineKeyboardButton("🗑️ Remove", callback_data=f"channel:remove:{channel_id}")
                ],
                [InlineKeyboardButton("🔙 Back to Channels", callback_data="menu:my_channels")]
            ]
            
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await query.edit_message_text(
                text,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            logger.error(f"Error showing channel info: {e}")
            await query.answer("Error loading channel information", show_alert=True)
    
    async def show_settings_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show settings menu for all channels"""
        
        query = update.callback_query
        user_id = update.effective_user.id
        
        # Get user channels
        user_channels = await self.channel_service.get_user_channels(user_id)
        
        if not user_channels:
            text = """
⚙️ **SETTINGS**

No channels to configure yet.

Add a channel first to access settings and customization options.
"""
            
            keyboard = [
                [InlineKeyboardButton("➕ Add Channel", callback_data="menu:add_channel")],
                [InlineKeyboardButton("🔙 Back to Menu", callback_data="menu:main")]
            ]
        else:
            text = f"""
⚙️ **SETTINGS**

Configure your channels for optimal performance:

**Available Settings:**
• Custom reaction emojis
• Reaction timing and delays
• Number of reactions per post
• Randomization preferences
• Auto-reaction toggle

**Select a channel to configure:**
"""
            
            keyboard = []
            
            for channel in user_channels[:8]:  # Limit for UI
                channel_name = channel.get("channel_title", "Unknown")[:30]
                if len(channel.get("channel_title", "")) > 30:
                    channel_name += "..."
                
                keyboard.append([
                    InlineKeyboardButton(
                        f"⚙️ {channel_name}",
                        callback_data=f"settings:channel:{channel['_id']}"
                    )
                ])
            
            keyboard.extend([
                [InlineKeyboardButton("🎛️ Global Settings", callback_data="settings:global")],
                [InlineKeyboardButton("🔙 Back to Menu", callback_data="menu:main")]
            ])
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
    
    async def show_dashboard(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show comprehensive user dashboard"""
        
        query = update.callback_query
        user_id = update.effective_user.id
        
        # Get user statistics
        credit_stats = await self.credit_service.get_user_statistics(user_id)
        user_channels = await self.channel_service.get_user_channels(user_id)
        
        # Get time-based statistics
        now = datetime.now(timezone.utc)
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        week_start = today_start - timedelta(days=7)
        month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        
        # Get reaction statistics
        reactions_today = await self.db.reaction_tasks.count_documents({
            "telegram_user_id": user_id,
            "created_at": {"$gte": today_start}
        })
        
        reactions_week = await self.db.reaction_tasks.count_documents({
            "telegram_user_id": user_id,
            "created_at": {"$gte": week_start}
        })
        
        reactions_month = await self.db.reaction_tasks.count_documents({
            "telegram_user_id": user_id,
            "created_at": {"$gte": month_start}
        })
        
        successful_today = await self.db.reaction_tasks.count_documents({
            "telegram_user_id": user_id,
            "created_at": {"$gte": today_start},
            "status": "completed"
        })
        
        success_rate = (successful_today / reactions_today * 100) if reactions_today > 0 else 0
        
        # Get channel performance
        active_channels = len([c for c in user_channels if c.get("is_active", False)])
        verified_channels = len([c for c in user_channels if c.get("verification_status") == "verified"])
        
        text = f"""
📊 **DASHBOARD**

**💰 Credit Overview:**
Current Balance: {credit_stats.get('current_balance', 0):,} credits
Today's Usage: {credit_stats.get('daily_spent', 0)} credits
Monthly Usage: {credit_stats.get('monthly_spent', 0)} credits
Remaining: {credit_stats.get('current_balance', 0) - credit_stats.get('daily_spent', 0):,} credits

**🎭 Reaction Statistics:**
Today: {reactions_today} reactions ({success_rate:.1f}% success)
This Week: {reactions_week} reactions
This Month: {reactions_month} reactions

**📺 Channel Performance:**
Active Channels: {active_channels}/{len(user_channels)}
Verified Channels: {verified_channels}/{len(user_channels)}
Total Channels: {len(user_channels)}

**📈 Performance Metrics:**
Success Rate: {success_rate:.1f}%
Average per Day: {reactions_month // 30 if reactions_month > 0 else 0} reactions
System Status: {"🟢 Excellent" if success_rate >= 95 else "🟡 Good" if success_rate >= 80 else "🔴 Needs Attention"}

**📅 Account Info:**
Member Since: {credit_stats.get('member_since', 'Recently')}
Subscription: {credit_stats.get('subscription_tier', 'free').title()}
"""
        
        keyboard = [
            [
                InlineKeyboardButton("📈 Detailed Analytics", callback_data="dashboard:analytics"),
                InlineKeyboardButton("💰 Credit History", callback_data="dashboard:credits")
            ],
            [
                InlineKeyboardButton("📺 Channel Stats", callback_data="dashboard:channels"),
                InlineKeyboardButton("🔄 Refresh Data", callback_data="menu:dashboard")
            ],
            [InlineKeyboardButton("🔙 Back to Menu", callback_data="menu:main")]
        ]
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
