# Docker Compose configuration for Auto-Reaction Bot

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: auto-reaction-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD:-admin123}
      MONGO_INITDB_DATABASE: telegram_reaction_bot
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init:/docker-entrypoint-initdb.d
    networks:
      - bot-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis for Caching and Task Queue
  redis:
    image: redis:7.2-alpine
    container_name: auto-reaction-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - bot-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Main Telegram Bot Service
  bot:
    build:
      context: .
      dockerfile: Dockerfile.bot
    container_name: auto-reaction-bot
    restart: unless-stopped
    environment:
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - TELEGRAM_API_ID=${TELEGRAM_API_ID}
      - TELEGRAM_API_HASH=${TELEGRAM_API_HASH}
      - MONGODB_URL=mongodb://${MONGO_ROOT_USERNAME:-admin}:${MONGO_ROOT_PASSWORD:-admin123}@mongodb:27017/telegram_reaction_bot?authSource=admin
      - REDIS_URL=redis://redis:6379/0
      - ADMIN_USER_IDS=${ADMIN_USER_IDS}
      - SERVICE_TYPE=bot
    ports:
      - "8000:8000"
    volumes:
      - ./sessions:/app/sessions
      - ./logs:/app/logs
      - ./data:/app/data
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - bot-network
    healthcheck:
      test: ["CMD", "python", "-c", "print('Bot healthy')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Task Processor Worker Service
  worker:
    build:
      context: .
      dockerfile: Dockerfile.worker
    restart: unless-stopped
    environment:
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - TELEGRAM_API_ID=${TELEGRAM_API_ID}
      - TELEGRAM_API_HASH=${TELEGRAM_API_HASH}
      - MONGODB_URL=mongodb://${MONGO_ROOT_USERNAME:-admin}:${MONGO_ROOT_PASSWORD:-admin123}@mongodb:27017/telegram_reaction_bot?authSource=admin
      - REDIS_URL=redis://redis:6379/0
      - SERVICE_TYPE=worker
    volumes:
      - ./sessions:/app/sessions
      - ./logs:/app/logs
      - ./data:/app/data
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - bot-network
    deploy:
      replicas: 2
    healthcheck:
      test: ["CMD", "python", "-c", "print('Worker healthy')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # API/Webhook Service
  api:
    build:
      context: .
      dockerfile: Dockerfile.api
    container_name: auto-reaction-api
    restart: unless-stopped
    environment:
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - MONGODB_URL=mongodb://${MONGO_ROOT_USERNAME:-admin}:${MONGO_ROOT_PASSWORD:-admin123}@mongodb:27017/telegram_reaction_bot?authSource=admin
      - REDIS_URL=redis://redis:6379/0
      - SERVICE_TYPE=api
    ports:
      - "8080:8080"
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - bot-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Scheduler Service
  scheduler:
    build:
      context: .
      dockerfile: Dockerfile.scheduler
    container_name: auto-reaction-scheduler
    restart: unless-stopped
    environment:
      - MONGODB_URL=mongodb://${MONGO_ROOT_USERNAME:-admin}:${MONGO_ROOT_PASSWORD:-admin123}@mongodb:27017/telegram_reaction_bot?authSource=admin
      - REDIS_URL=redis://redis:6379/0
      - SERVICE_TYPE=scheduler
    volumes:
      - ./logs:/app/logs
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - bot-network
    healthcheck:
      test: ["CMD", "python", "-c", "import redis; r = redis.Redis(host='redis', port=6379); r.ping()"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Flower Monitoring (Optional)
  flower:
    build:
      context: .
      dockerfile: Dockerfile.flower
    container_name: auto-reaction-flower
    restart: unless-stopped
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
    ports:
      - "5555:5555"
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - bot-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5555/"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  bot-network:
    driver: bridge
