# Dockerfile for Scheduler Service
FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/app
ENV SERVICE_TYPE=scheduler

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    libffi-dev \
    libssl-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Copy requirements and install dependencies
COPY code/requirements.txt /app/requirements.txt
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir celery redis

# Copy application code
COPY . /app/

# Create necessary directories
RUN mkdir -p /app/logs /app/sessions /app/data

# Create non-root user
RUN useradd -m -u 1000 botuser && \
    chown -R botuser:botuser /app
USER botuser

# Health check for scheduler service
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD python -c "import redis; r = redis.Redis(host='redis', port=6379); r.ping(); print('Scheduler healthy')" || exit 1

# Start scheduler service (using APScheduler for now)
CMD ["python", "-c", "import asyncio; import time; from apscheduler.schedulers.asyncio import AsyncIOScheduler; scheduler = AsyncIOScheduler(); scheduler.start(); print('Scheduler started'); asyncio.get_event_loop().run_forever()"]
