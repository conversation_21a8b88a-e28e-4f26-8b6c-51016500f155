# Telegram Auto-Reaction Bot - Infrastructure Services
# No version needed for modern Docker Compose

services:
  # Redis for Celery and caching
  redis:
    image: redis:7-alpine
    container_name: telegram_bot_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB (for development - use Atlas in production)
  mongodb:
    image: mongo:7
    container_name: telegram_bot_mongodb
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: telegram_reaction_bot
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Main Telegram Bot
  bot:
    build:
      context: .
      dockerfile: docker/Dockerfile.bot
    container_name: telegram_bot_main
    depends_on:
      - redis
      - mongodb
    environment:
      - REDIS_URL=redis://redis:6379/0
      - MONGODB_URL=*****************************************************************************
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
      - ./sessions:/app/sessions
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3

  # FastAPI Server
  api:
    build:
      context: .
      dockerfile: docker/Dockerfile.api
    container_name: telegram_bot_api
    ports:
      - "8000:8000"
    depends_on:
      - redis
      - mongodb
    environment:
      - REDIS_URL=redis://redis:6379/0
      - MONGODB_URL=*****************************************************************************
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker for Reactions
  worker:
    build:
      context: .
      dockerfile: docker/Dockerfile.worker
    depends_on:
      - redis
      - mongodb
    environment:
      - REDIS_URL=redis://redis:6379/0
      - MONGODB_URL=*****************************************************************************
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
      - ./sessions:/app/sessions
    restart: unless-stopped
    deploy:
      replicas: 2

  # Celery Beat Scheduler
  scheduler:
    build:
      context: .
      dockerfile: docker/Dockerfile.scheduler
    container_name: telegram_bot_scheduler
    depends_on:
      - redis
      - mongodb
    environment:
      - REDIS_URL=redis://redis:6379/0
      - MONGODB_URL=*****************************************************************************
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  # Flower for Celery monitoring
  flower:
    build:
      context: .
      dockerfile: docker/Dockerfile.flower
    container_name: telegram_bot_flower
    ports:
      - "5555:5555"
    depends_on:
      - redis
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
    env_file:
      - .env
    restart: unless-stopped

  # Prometheus for metrics
  prometheus:
    image: prom/prometheus:latest
    container_name: telegram_bot_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    restart: unless-stopped

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: telegram_bot_grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    restart: unless-stopped

volumes:
  redis_data:
  mongodb_data:
  prometheus_data:
  grafana_data:

networks:
  default:
    name: telegram_bot_network
