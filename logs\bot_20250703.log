2025-07-03 07:10:45,181 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:10:53,856 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:10:53,860 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/telegram/ext/_application.py", line 1234, in process_update
    await coroutine
  File "/usr/local/lib/python3.11/site-packages/telegram/ext/_basehandler.py", line 157, in handle_update
    return await self.callback(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/auto_reaction_bot.py", line 664, in handle_text_message
    text = message.text.strip()
           ^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'text'
2025-07-03 07:11:04,077 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:11:14,259 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:11:24,452 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:11:34,645 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:11:44,837 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:11:55,025 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:12:05,217 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:12:15,412 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:12:25,602 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:12:35,794 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:12:45,996 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:12:56,202 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:13:06,396 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:13:16,593 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:13:26,782 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:13:36,965 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:13:47,158 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:13:57,348 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:14:03,972 - services.userbot_manager - INFO - Performing health check on all sessions...
2025-07-03 07:14:03,972 - services.userbot_manager - INFO - Performing health check on all sessions...
2025-07-03 07:14:03,999 - src.workers.enhanced_task_processor - CRITICAL - No healthy userbot sessions available!
2025-07-03 07:14:04,000 - src.workers.enhanced_task_processor - CRITICAL - No healthy userbot sessions available!
2025-07-03 07:14:04,002 - src.workers.enhanced_task_processor - INFO - System health: 0/0 sessions, 0 pending tasks
2025-07-03 07:14:04,002 - src.workers.enhanced_task_processor - INFO - System health: 0/0 sessions, 0 pending tasks
2025-07-03 07:14:07,530 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:14:17,744 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:14:27,951 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:14:38,156 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:14:48,351 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:14:58,549 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:15:08,734 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:15:18,926 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:15:29,111 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:15:39,300 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:15:49,485 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:15:59,679 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:16:09,885 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:16:20,139 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:16:30,327 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:16:40,523 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:16:50,714 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:17:00,909 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:17:11,101 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:17:21,287 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:17:31,473 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:17:41,661 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:17:51,843 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:18:02,015 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:18:12,190 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:18:22,420 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:18:32,603 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:18:42,788 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:18:52,981 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:19:03,168 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:19:03,944 - services.userbot_manager - INFO - Performing health check on all sessions...
2025-07-03 07:19:03,945 - services.userbot_manager - INFO - Performing health check on all sessions...
2025-07-03 07:19:03,981 - src.workers.enhanced_task_processor - CRITICAL - No healthy userbot sessions available!
2025-07-03 07:19:03,982 - src.workers.enhanced_task_processor - CRITICAL - No healthy userbot sessions available!
2025-07-03 07:19:03,983 - src.workers.enhanced_task_processor - INFO - System health: 0/0 sessions, 0 pending tasks
2025-07-03 07:19:03,984 - src.workers.enhanced_task_processor - INFO - System health: 0/0 sessions, 0 pending tasks
2025-07-03 07:19:13,358 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:19:23,955 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:19:34,140 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:19:44,325 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:19:54,519 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:20:04,702 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:20:14,911 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:20:25,091 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:20:35,275 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:20:45,458 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:20:55,636 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:21:05,817 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:21:15,999 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:21:26,175 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:21:36,361 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:21:46,563 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:21:56,747 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:22:06,928 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:22:17,105 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:22:27,290 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:22:37,475 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:22:47,650 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:22:57,834 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:23:08,023 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:23:18,214 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:23:28,399 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:23:38,574 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:23:48,771 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:23:58,940 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:24:03,918 - services.userbot_manager - INFO - Performing health check on all sessions...
2025-07-03 07:24:03,920 - services.userbot_manager - INFO - Performing health check on all sessions...
2025-07-03 07:24:03,966 - src.workers.enhanced_task_processor - CRITICAL - No healthy userbot sessions available!
2025-07-03 07:24:03,968 - src.workers.enhanced_task_processor - CRITICAL - No healthy userbot sessions available!
2025-07-03 07:24:03,970 - src.workers.enhanced_task_processor - INFO - System health: 0/0 sessions, 0 pending tasks
2025-07-03 07:24:03,972 - src.workers.enhanced_task_processor - INFO - System health: 0/0 sessions, 0 pending tasks
2025-07-03 07:24:09,125 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:24:19,310 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:24:29,499 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:24:39,688 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:24:49,872 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:25:00,053 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:25:10,237 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:25:20,421 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:25:30,591 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:25:40,772 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:25:50,956 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:26:01,138 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:26:11,321 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:26:21,498 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:26:31,677 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:26:41,864 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:26:52,055 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:27:02,239 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:27:12,427 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:27:22,615 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:27:32,796 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:27:42,988 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:27:53,173 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:28:03,356 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:28:13,552 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:28:23,734 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:28:33,906 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:28:44,079 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:28:54,266 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:29:03,896 - services.userbot_manager - INFO - Performing health check on all sessions...
2025-07-03 07:29:03,898 - services.userbot_manager - INFO - Performing health check on all sessions...
2025-07-03 07:29:03,959 - src.workers.enhanced_task_processor - CRITICAL - No healthy userbot sessions available!
2025-07-03 07:29:03,961 - src.workers.enhanced_task_processor - CRITICAL - No healthy userbot sessions available!
2025-07-03 07:29:03,962 - src.workers.enhanced_task_processor - INFO - System health: 0/0 sessions, 0 pending tasks
2025-07-03 07:29:03,965 - src.workers.enhanced_task_processor - INFO - System health: 0/0 sessions, 0 pending tasks
2025-07-03 07:29:04,453 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:29:14,637 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:29:24,821 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:29:35,007 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:29:45,192 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:29:55,378 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:30:05,564 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:30:15,749 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:30:25,943 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:30:36,128 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:30:46,313 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:30:56,495 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:31:06,680 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:31:16,862 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:31:27,053 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:31:37,250 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:31:47,437 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:31:57,623 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:32:07,812 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:32:18,025 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:32:28,206 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:32:38,380 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:32:48,573 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:32:58,768 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:33:08,953 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7792678353:AAGHzLCuY-fSV-PLW9EiDEmqGNtJmEjGZlw/getUpdates "HTTP/1.1 200 OK"
2025-07-03 07:35:07,002 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:35:07,317 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:35:07,317 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:35:07,322 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:35:09,471 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:35:09,793 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:35:09,794 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:35:09,798 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:35:11,846 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:35:12,157 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:35:12,158 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:35:12,162 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:35:14,242 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:35:14,541 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:35:14,557 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:35:14,562 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:35:17,031 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:35:17,347 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:35:17,348 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:35:17,353 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:35:20,539 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:35:20,848 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:35:20,849 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:35:20,854 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:35:25,601 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:35:25,892 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:35:25,893 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:35:25,897 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:35:33,953 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:35:34,243 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:35:34,243 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:35:34,252 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:35:48,660 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:35:48,981 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:35:49,005 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:35:49,022 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:36:16,468 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:36:16,796 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:36:16,797 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:36:16,805 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:36:26,733 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:36:27,036 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:36:27,037 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:36:27,041 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:36:29,113 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:36:29,393 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:36:29,394 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:36:29,398 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:36:31,534 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:36:31,855 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:36:31,856 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:36:31,862 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:36:34,012 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:36:34,295 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:36:34,296 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:36:34,300 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:36:36,690 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:36:36,985 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:36:36,986 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:36:36,990 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:36:40,277 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:36:40,635 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:36:40,635 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:36:40,639 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:36:45,918 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:36:46,257 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:36:46,260 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:36:46,268 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:36:54,451 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:36:54,859 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:36:54,860 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:36:54,864 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:37:34,083 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:37:34,462 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:37:34,463 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:37:34,468 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:37:36,646 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:37:36,935 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:37:36,937 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:37:36,943 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:37:39,073 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:37:39,363 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:37:39,364 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:37:39,368 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:37:41,430 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:37:41,739 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:37:41,740 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:37:41,745 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:37:44,100 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:37:44,390 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:37:44,391 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:37:44,394 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:37:47,752 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:37:48,080 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:37:48,081 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:37:48,086 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:37:52,894 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:37:53,182 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:37:53,183 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:37:53,190 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:38:01,242 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:38:01,542 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:38:01,543 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:38:01,547 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:38:15,938 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:38:16,253 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:38:16,254 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:38:16,259 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:38:39,819 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:38:40,157 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:38:40,159 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:38:40,165 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:38:42,222 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:38:42,563 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:38:42,572 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:38:42,576 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:38:44,713 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:38:45,016 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:38:45,017 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:38:45,021 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:38:46,997 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:38:47,295 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:38:47,296 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:38:47,304 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:38:49,834 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:38:50,124 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:38:50,125 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:38:50,132 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:38:53,392 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:38:53,709 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:38:53,710 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:38:53,716 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:38:58,543 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:38:58,903 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:38:58,903 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:38:58,907 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:39:06,966 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:39:07,290 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:39:07,291 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:39:07,295 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:39:21,763 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:39:22,044 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:39:22,045 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:39:22,049 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:39:34,971 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:39:35,292 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:39:35,293 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:39:35,297 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:39:37,407 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:39:37,699 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:39:37,699 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:39:37,704 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:39:39,653 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:39:39,948 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:39:39,949 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:39:39,953 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:39:42,075 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:39:42,429 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:39:42,430 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:39:42,438 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:39:44,815 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:39:45,103 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:39:45,104 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:39:45,108 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:39:48,165 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:39:48,443 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:39:48,444 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:39:48,448 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:39:53,146 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:39:53,442 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:39:53,443 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:39:53,447 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:40:01,327 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:40:01,609 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:40:01,610 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:40:01,614 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
2025-07-03 07:40:15,918 - pyrogram.crypto.aes - INFO - Using TgCrypto
2025-07-03 07:40:16,186 - auto_reaction_bot - INFO - Successfully imported Phase 5 core services
2025-07-03 07:40:16,187 - auto_reaction_bot - INFO - Successfully imported all Phase 5 services and UI handlers
2025-07-03 07:40:16,191 - __main__ - ERROR - Fatal error: Fernet key must be 32 url-safe base64-encoded bytes.
