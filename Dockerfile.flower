# Dockerfile for Flower Monitoring Service
FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV SERVICE_TYPE=flower

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    libffi-dev \
    libssl-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Install Flower and dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir celery flower redis

# Create non-root user
RUN useradd -m -u 1000 botuser && \
    chown -R botuser:botuser /app
USER botuser

# Expose Flower port
EXPOSE 5555

# Health check for Flower service
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:5555/ || exit 1

# Start Flower monitoring
CMD ["celery", "flower", "--broker=redis://redis:6379/0", "--port=5555", "--basic_auth=admin:admin123"]
