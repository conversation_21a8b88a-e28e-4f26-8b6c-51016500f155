#!/usr/bin/env python3
"""
Webhook Service - Real-time Channel Post Processing
"""

import asyncio
import random
import os
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone, timedelta
from motor.motor_asyncio import AsyncIOMotorClient
from bson import ObjectId
import logging

# Add src to path for imports
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from services.channel_service import ChannelService
from services.credit_service import CreditService

logger = logging.getLogger(__name__)

class WebhookService:
    """Service for processing channel posts via webhooks and automated monitoring"""
    
    def __init__(self, db_client: AsyncIOMotorClient):
        self.db = db_client.telegram_reaction_bot
        self.channel_service = ChannelService(db_client)
        self.credit_service = CreditService(db_client)
        
    async def process_channel_post(self, update_data: Dict[str, Any]) -> bool:
        """Process incoming channel post from webhook or monitoring"""
        
        try:
            # Extract channel post data
            if "channel_post" not in update_data:
                return False
            
            post = update_data["channel_post"]
            channel_id = post["chat"]["id"]
            message_id = post["message_id"]
            
            # Check if channel is managed by our system
            channel_doc = await self.db.channels.find_one({
                "channel_id": channel_id,
                "is_active": True,
                "verification_status": "verified"
            })
            
            if not channel_doc:
                logger.debug(f"Channel {channel_id} not managed or inactive")
                return False
            
            # Check if post already processed
            existing_post = await self.db.channel_posts.find_one({
                "channel_id": channel_id,
                "message_id": message_id
            })
            
            if existing_post:
                logger.debug(f"Post {channel_id}:{message_id} already processed")
                return False
            
            # Create channel post record
            post_doc = {
                "channel_id": channel_id,
                "message_id": message_id,
                "channel_doc_id": str(channel_doc["_id"]),
                "post_text": post.get("text", ""),
                "post_type": self.determine_post_type(post),
                "post_date": datetime.fromtimestamp(post["date"], tz=timezone.utc),
                "is_processed": False,
                "reactions_to_send": channel_doc["reactions_per_post"],
                "reactions_sent": 0,
                "tasks_created": [],
                "discovered_at": datetime.now(timezone.utc)
            }
            
            result = await self.db.channel_posts.insert_one(post_doc)
            
            # Schedule reaction tasks
            await self.schedule_reaction_tasks(
                channel_doc, 
                channel_id, 
                message_id, 
                str(result.inserted_id)
            )
            
            logger.info(f"Processed new post {channel_id}:{message_id} from {channel_doc['channel_title']}")
            return True
            
        except Exception as e:
            logger.error(f"Error processing channel post: {e}")
            await self.log_processing_error(update_data, str(e))
            return False
    
    async def schedule_reaction_tasks(
        self, 
        channel_doc: Dict[str, Any], 
        channel_id: int, 
        message_id: int,
        post_doc_id: str
    ):
        """Schedule reaction tasks for a channel post"""
        
        owner_user_id = channel_doc["owner_user_id"]
        reactions_to_send = channel_doc["reactions_per_post"]
        
        # Check user's credit balance
        credit_balance = await self.credit_service.get_user_balance(owner_user_id)
        
        if credit_balance < reactions_to_send:
            # Insufficient credits - pause channel and notify
            await self.channel_service.pause_channel(channel_id, owner_user_id)
            
            await self.queue_notification(
                owner_user_id,
                f"Channel '{channel_doc['channel_title']}' paused due to insufficient credits. "
                f"Required: {reactions_to_send}, Available: {credit_balance}. "
                f"Please add more credits to resume automatic reactions."
            )
            
            logger.warning(f"Channel {channel_id} paused due to insufficient credits")
            return
        
        # Select emojis based on reaction mode
        emojis = self.select_emojis(channel_doc, reactions_to_send)
        
        task_ids = []
        base_delay = random.randint(
            channel_doc["reaction_delay_min"], 
            channel_doc["reaction_delay_max"]
        )
        
        for i, emoji in enumerate(emojis):
            # Calculate staggered delay
            delay = base_delay + (i * random.randint(5, 15))  # Stagger reactions
            
            # Create reaction task
            task_doc = {
                "channel_id": channel_id,
                "message_id": message_id,
                "emoji": emoji,
                "telegram_user_id": owner_user_id,
                "channel_doc_id": str(channel_doc["_id"]),
                "assigned_session": None,
                "attempts": 0,
                "max_attempts": 3,
                "credits_consumed": 1,
                "credit_transaction_id": None,
                "status": "pending",
                "created_at": datetime.now(timezone.utc),
                "scheduled_at": datetime.now(timezone.utc) + timedelta(seconds=delay)
            }
            
            result = await self.db.reaction_tasks.insert_one(task_doc)
            task_ids.append(str(result.inserted_id))
        
        # Update post record
        await self.db.channel_posts.update_one(
            {"_id": ObjectId(post_doc_id)},
            {
                "$set": {
                    "tasks_created": task_ids,
                    "is_processed": True,
                    "processed_at": datetime.now(timezone.utc)
                }
            }
        )
        
        logger.info(f"Scheduled {len(task_ids)} reaction tasks for post {channel_id}:{message_id}")
    
    def select_emojis(self, channel_doc: Dict[str, Any], count: int) -> List[str]:
        """Select emojis based on channel reaction mode"""
        
        emoji_list = channel_doc["emoji_list"]
        reaction_mode = channel_doc["reaction_mode"]
        
        if reaction_mode == "fixed":
            # Use first N emojis
            return emoji_list[:count]
        
        elif reaction_mode == "sequential":
            # Use emojis in sequence, cycling if needed
            selected = []
            for i in range(count):
                selected.append(emoji_list[i % len(emoji_list)])
            return selected
        
        else:  # random
            # Select random emojis (can repeat)
            return [random.choice(emoji_list) for _ in range(count)]
    
    def determine_post_type(self, post: Dict[str, Any]) -> str:
        """Determine the type of channel post"""
        
        if "photo" in post:
            return "photo"
        elif "video" in post:
            return "video"
        elif "document" in post:
            return "document"
        elif "audio" in post:
            return "audio"
        elif "voice" in post:
            return "voice"
        elif "animation" in post:
            return "animation"
        elif "sticker" in post:
            return "sticker"
        elif "poll" in post:
            return "poll"
        else:
            return "text"
    
    async def queue_notification(self, user_id: int, message: str, notification_type: str = "warning"):
        """Queue notification for user"""
        
        notification = {
            "telegram_user_id": user_id,
            "message": message,
            "notification_type": notification_type,
            "status": "pending",
            "created_at": datetime.now(timezone.utc)
        }
        
        await self.db.notifications.insert_one(notification)
        logger.info(f"Queued notification for user {user_id}: {notification_type}")
    
    async def log_processing_error(self, update_data: Dict[str, Any], error: str):
        """Log processing error for debugging"""
        
        error_log = {
            "update_data": update_data,
            "error": error,
            "timestamp": datetime.now(timezone.utc),
            "service": "webhook_service"
        }
        
        await self.db.processing_errors.insert_one(error_log)
    
    async def simulate_channel_posts(self):
        """Simulate channel posts for testing (Phase 4 development)"""
        
        logger.info("Starting channel post simulation for testing...")
        
        while True:
            try:
                # Get active channels
                active_channels = await self.db.channels.find({
                    "is_active": True,
                    "verification_status": "verified"
                }).to_list(length=None)
                
                if not active_channels:
                    await asyncio.sleep(60)
                    continue
                
                # Randomly select a channel to simulate a post
                channel = random.choice(active_channels)
                
                # Create simulated post data
                simulated_post = {
                    "channel_post": {
                        "message_id": random.randint(1000, 9999),
                        "chat": {
                            "id": channel["channel_id"],
                            "title": channel["channel_title"],
                            "type": "channel"
                        },
                        "date": int(datetime.now(timezone.utc).timestamp()),
                        "text": f"Simulated post for testing - {datetime.now().strftime('%H:%M:%S')}"
                    }
                }
                
                # Process the simulated post
                await self.process_channel_post(simulated_post)
                
                # Wait random interval between posts (30 seconds to 5 minutes)
                wait_time = random.randint(30, 300)
                logger.info(f"Next simulated post in {wait_time} seconds")
                await asyncio.sleep(wait_time)
                
            except Exception as e:
                logger.error(f"Error in post simulation: {e}")
                await asyncio.sleep(60)
    
    async def monitor_channels_for_posts(self):
        """Monitor channels for new posts (alternative to webhooks)"""

        logger.info("Channel monitoring disabled - using main bot long polling for post detection")

        # Post detection is now handled by the main bot through long polling
        # This service will only process posts sent from the main bot
        logger.info("Webhook service ready to process posts from main bot")

        # Keep the service alive but don't simulate posts
        while True:
            await asyncio.sleep(300)  # Check every 5 minutes for any pending tasks
            await self.process_pending_notifications()
    
    async def process_pending_notifications(self):
        """Process pending user notifications"""
        
        while True:
            try:
                # Get pending notifications
                notifications = await self.db.notifications.find({
                    "status": "pending"
                }).limit(50).to_list(length=50)
                
                for notification in notifications:
                    try:
                        # Here we would send the notification via the bot
                        # For now, just mark as processed
                        await self.db.notifications.update_one(
                            {"_id": notification["_id"]},
                            {
                                "$set": {
                                    "status": "sent",
                                    "sent_at": datetime.now(timezone.utc)
                                }
                            }
                        )
                        
                        logger.info(f"Processed notification for user {notification['telegram_user_id']}")
                        
                    except Exception as e:
                        logger.error(f"Error processing notification {notification['_id']}: {e}")
                        
                        # Mark as failed
                        await self.db.notifications.update_one(
                            {"_id": notification["_id"]},
                            {
                                "$set": {
                                    "status": "failed",
                                    "error": str(e)
                                }
                            }
                        )
                
                # Wait before next batch
                await asyncio.sleep(30)
                
            except Exception as e:
                logger.error(f"Error in notification processing: {e}")
                await asyncio.sleep(60)
    
    async def get_processing_statistics(self) -> Dict[str, Any]:
        """Get webhook processing statistics"""
        
        try:
            # Get today's statistics
            today_start = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
            
            posts_today = await self.db.channel_posts.count_documents({
                "discovered_at": {"$gte": today_start}
            })
            
            processed_today = await self.db.channel_posts.count_documents({
                "discovered_at": {"$gte": today_start},
                "is_processed": True
            })
            
            tasks_created_today = await self.db.reaction_tasks.count_documents({
                "created_at": {"$gte": today_start}
            })
            
            # Get pending notifications
            pending_notifications = await self.db.notifications.count_documents({
                "status": "pending"
            })
            
            # Get error count
            errors_today = await self.db.processing_errors.count_documents({
                "timestamp": {"$gte": today_start}
            })
            
            return {
                "posts_discovered_today": posts_today,
                "posts_processed_today": processed_today,
                "tasks_created_today": tasks_created_today,
                "pending_notifications": pending_notifications,
                "errors_today": errors_today,
                "processing_rate": (processed_today / posts_today * 100) if posts_today > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"Error getting processing statistics: {e}")
            return {}
    
    async def cleanup_old_data(self):
        """Clean up old processed data"""
        
        while True:
            try:
                await asyncio.sleep(3600)  # Run every hour
                
                # Clean up old channel posts (older than 30 days)
                cutoff_date = datetime.now(timezone.utc) - timedelta(days=30)
                
                posts_result = await self.db.channel_posts.delete_many({
                    "discovered_at": {"$lt": cutoff_date},
                    "is_processed": True
                })
                
                # Clean up old notifications (older than 7 days)
                notification_cutoff = datetime.now(timezone.utc) - timedelta(days=7)
                
                notifications_result = await self.db.notifications.delete_many({
                    "created_at": {"$lt": notification_cutoff},
                    "status": {"$in": ["sent", "failed"]}
                })
                
                # Clean up old processing errors (older than 14 days)
                error_cutoff = datetime.now(timezone.utc) - timedelta(days=14)
                
                errors_result = await self.db.processing_errors.delete_many({
                    "timestamp": {"$lt": error_cutoff}
                })
                
                if (posts_result.deleted_count > 0 or 
                    notifications_result.deleted_count > 0 or 
                    errors_result.deleted_count > 0):
                    
                    logger.info(f"Cleanup completed: {posts_result.deleted_count} posts, "
                              f"{notifications_result.deleted_count} notifications, "
                              f"{errors_result.deleted_count} errors")
                
            except Exception as e:
                logger.error(f"Error in cleanup: {e}")
    
    async def start_background_services(self):
        """Start all background services"""
        
        logger.info("Starting webhook service background tasks...")
        
        # Start all background tasks
        await asyncio.gather(
            self.monitor_channels_for_posts(),
            self.process_pending_notifications(),
            self.cleanup_old_data(),
            return_exceptions=True
        )
