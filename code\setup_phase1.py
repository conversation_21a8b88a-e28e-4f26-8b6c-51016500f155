#!/usr/bin/env python3
"""
Phase 1 Setup - Database Schema & Credit System
"""

import asyncio
import os
import sys
from datetime import datetime, timezone
from motor.motor_asyncio import AsyncIOMotorClient
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def setup_phase1():
    """Set up Phase 1: Enhanced database schema and credit system"""
    
    print("🚀 PHASE 1 IMPLEMENTATION")
    print("Database Schema & Credit System Setup")
    print("=" * 60)
    
    # Connect to MongoDB
    mongodb_url = os.getenv('MONGODB_URL', 'mongodb://localhost:27017/telegram_reaction_bot')
    print(f"📡 Connecting to MongoDB: {mongodb_url}")
    
    client = AsyncIOMotorClient(mongodb_url)
    db = client.telegram_reaction_bot
    
    try:
        # Test connection
        await client.admin.command('ping')
        print("✅ Connected to MongoDB")
        
        # Create indexes for better performance
        print("\n📊 Creating database indexes...")
        
        # Users collection indexes
        try:
            await db.users.create_index("telegram_user_id", unique=True)
            await db.users.create_index([("is_active", 1), ("created_at", -1)])
            await db.users.create_index([("subscription_tier", 1), ("credit_balance", -1)])
            print("✅ Users indexes created")
        except Exception as e:
            print(f"⚠️  Users indexes: {e}")
        
        # Channels collection indexes
        try:
            await db.channels.create_index("channel_id", unique=True)
            await db.channels.create_index([("owner_user_id", 1), ("is_active", 1)])
            await db.channels.create_index([("verification_status", 1), ("created_at", -1)])
            await db.channels.create_index("verification_token")
            print("✅ Channels indexes created")
        except Exception as e:
            print(f"⚠️  Channels indexes: {e}")
        
        # Credit transactions indexes
        try:
            await db.credit_transactions.create_index([("telegram_user_id", 1), ("created_at", -1)])
            await db.credit_transactions.create_index([("transaction_type", 1), ("created_at", -1)])
            await db.credit_transactions.create_index("reference_id")
            await db.credit_transactions.create_index([("status", 1), ("created_at", -1)])
            print("✅ Credit transactions indexes created")
        except Exception as e:
            print(f"⚠️  Credit transactions indexes: {e}")

        # Reaction tasks indexes
        try:
            await db.reaction_tasks.create_index([("status", 1), ("scheduled_at", 1)])
            await db.reaction_tasks.create_index([("channel_id", 1), ("created_at", -1)])
            await db.reaction_tasks.create_index([("telegram_user_id", 1), ("status", 1)])
            await db.reaction_tasks.create_index("assigned_session")
            await db.reaction_tasks.create_index("credit_transaction_id")
            print("✅ Reaction tasks indexes created")
        except Exception as e:
            print(f"⚠️  Reaction tasks indexes: {e}")
        
        # Upgrade existing users to new schema
        print("\n🔄 Upgrading existing users...")
        
        users_cursor = db.users.find({})
        user_count = 0
        async for user in users_cursor:
            # Add new fields if they don't exist
            update_data = {}
            
            if "credit_balance" not in user:
                update_data["credit_balance"] = 0
            if "total_credits_purchased" not in user:
                update_data["total_credits_purchased"] = 0
            if "total_credits_spent" not in user:
                update_data["total_credits_spent"] = 0
            if "subscription_tier" not in user:
                update_data["subscription_tier"] = "free"
            if "max_channels" not in user:
                update_data["max_channels"] = 1
            if "max_reactions_per_post" not in user:
                update_data["max_reactions_per_post"] = 3
            if "is_banned" not in user:
                update_data["is_banned"] = False
            if "last_activity" not in user:
                update_data["last_activity"] = None
            
            if update_data:
                update_data["updated_at"] = datetime.now(timezone.utc)
                await db.users.update_one(
                    {"_id": user["_id"]},
                    {"$set": update_data}
                )
                user_count += 1
        
        print(f"✅ {user_count} users upgraded to new schema")
        
        # Upgrade existing userbot sessions
        print("\n🤖 Upgrading userbot sessions...")
        
        sessions_cursor = db.userbot_sessions.find({})
        session_count = 0
        async for session in sessions_cursor:
            update_data = {}
            
            if "is_healthy" not in session:
                update_data["is_healthy"] = True
            if "last_health_check" not in session:
                update_data["last_health_check"] = None
            if "reactions_sent_today" not in session:
                update_data["reactions_sent_today"] = 0
            if "reactions_sent_total" not in session:
                update_data["reactions_sent_total"] = session.get("reactions_sent", 0)
            if "errors_count" not in session:
                update_data["errors_count"] = 0
            if "daily_limit" not in session:
                update_data["daily_limit"] = 1000
            if "hourly_limit" not in session:
                update_data["hourly_limit"] = 100
            if "current_hour_count" not in session:
                update_data["current_hour_count"] = 0
            if "is_banned" not in session:
                update_data["is_banned"] = False
            
            if update_data:
                update_data["updated_at"] = datetime.now(timezone.utc)
                await db.userbot_sessions.update_one(
                    {"_id": session["_id"]},
                    {"$set": update_data}
                )
                session_count += 1
        
        print(f"✅ {session_count} userbot sessions upgraded")
        
        # Create/upgrade admin user with credits
        print("\n👤 Setting up admin user...")
        
        admin_user_ids = os.getenv('ADMIN_USER_IDS', '').split(',')
        if admin_user_ids and admin_user_ids[0]:
            admin_id = int(admin_user_ids[0])
            
            # Check if admin user exists
            admin_user = await db.users.find_one({"telegram_user_id": admin_id})
            
            if admin_user:
                # Upgrade existing admin
                current_balance = admin_user.get("credit_balance", 0)
                new_balance = max(current_balance, 10000)  # Ensure at least 10,000 credits
                
                await db.users.update_one(
                    {"telegram_user_id": admin_id},
                    {
                        "$set": {
                            "credit_balance": new_balance,
                            "subscription_tier": "enterprise",
                            "max_channels": 1000,
                            "max_reactions_per_post": 50,
                            "updated_at": datetime.now(timezone.utc)
                        }
                    }
                )
                print(f"✅ Admin user {admin_id} upgraded with {new_balance} credits")
                
                # Add credits transaction if balance was increased
                if new_balance > current_balance:
                    credit_transaction = {
                        "user_id": admin_id,
                        "transaction_type": "bonus",
                        "amount": new_balance - current_balance,
                        "balance_before": current_balance,
                        "balance_after": new_balance,
                        "description": "Admin setup bonus credits",
                        "status": "completed",
                        "created_at": datetime.now(timezone.utc),
                        "processed_at": datetime.now(timezone.utc)
                    }
                    await db.credit_transactions.insert_one(credit_transaction)
                    
            else:
                # Create new admin user
                admin_doc = {
                    "telegram_user_id": admin_id,
                    "username": "admin",
                    "first_name": "Admin",
                    "credit_balance": 10000,
                    "total_credits_purchased": 10000,
                    "total_credits_spent": 0,
                    "subscription_tier": "enterprise",
                    "max_channels": 1000,
                    "max_reactions_per_post": 50,
                    "is_active": True,
                    "is_banned": False,
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }
                
                await db.users.insert_one(admin_doc)
                print(f"✅ Admin user {admin_id} created with 10,000 credits")
                
                # Add initial credit transaction
                credit_transaction = {
                    "user_id": admin_id,
                    "transaction_type": "bonus",
                    "amount": 10000,
                    "balance_before": 0,
                    "balance_after": 10000,
                    "description": "Initial admin credits",
                    "status": "completed",
                    "created_at": datetime.now(timezone.utc),
                    "processed_at": datetime.now(timezone.utc)
                }
                await db.credit_transactions.insert_one(credit_transaction)
        
        # Display summary
        print("\n📊 Phase 1 Setup Summary:")
        print("=" * 60)
        
        # Count documents
        users_count = await db.users.count_documents({})
        channels_count = await db.channels.count_documents({})
        sessions_count = await db.userbot_sessions.count_documents({})
        tasks_count = await db.reaction_tasks.count_documents({})
        transactions_count = await db.credit_transactions.count_documents({})
        
        print(f"👥 Users: {users_count}")
        print(f"📺 Channels: {channels_count}")
        print(f"🤖 Userbot Sessions: {sessions_count}")
        print(f"🎭 Reaction Tasks: {tasks_count}")
        print(f"💳 Credit Transactions: {transactions_count}")
        
        # Show total credits in system
        total_credits = await db.users.aggregate([
            {"$group": {"_id": None, "total": {"$sum": "$credit_balance"}}}
        ]).to_list(length=1)
        
        credits_in_system = total_credits[0]["total"] if total_credits else 0
        print(f"💰 Total Credits in System: {credits_in_system:,}")
        
        print("\n🎉 Phase 1 Setup Complete!")
        print("\n📋 Next Steps:")
        print("1. Test the credit system")
        print("2. Implement Phase 2 (Multi-Channel Management)")
        print("3. Continue with implementation plan")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ Phase 1 setup failed: {e}")
        client.close()
        return False

async def test_credit_system():
    """Test the credit system functionality"""
    
    print("\n🧪 Testing Credit System...")
    print("=" * 40)
    
    try:
        # Import the credit service
        sys.path.append('src')
        from services.credit_service import CreditService
        
        # Connect to database
        mongodb_url = os.getenv('MONGODB_URL', 'mongodb://localhost:27017/telegram_reaction_bot')
        client = AsyncIOMotorClient(mongodb_url)
        
        # Initialize credit service
        credit_service = CreditService(client)
        
        # Get admin user ID for testing
        admin_user_ids = os.getenv('ADMIN_USER_IDS', '').split(',')
        if admin_user_ids and admin_user_ids[0]:
            admin_id = int(admin_user_ids[0])
            
            # Test getting balance
            balance = await credit_service.get_user_balance(admin_id)
            print(f"✅ Get balance: {balance} credits")
            
            # Test consuming credits
            success, transaction_id = await credit_service.consume_credits(
                admin_id, 5, "Test consumption"
            )
            if success:
                print(f"✅ Consume credits: Transaction {transaction_id}")
            else:
                print(f"❌ Consume credits failed: {transaction_id}")
            
            # Test adding credits
            success, transaction_id = await credit_service.add_credits(
                admin_id, 10, "Test addition", "bonus"
            )
            if success:
                print(f"✅ Add credits: Transaction {transaction_id}")
            else:
                print(f"❌ Add credits failed: {transaction_id}")
            
            # Test getting statistics
            stats = await credit_service.get_user_statistics(admin_id)
            print(f"✅ User statistics: Balance {stats.get('current_balance', 0)}")
            
            print("\n🎉 Credit system tests passed!")
        
        client.close()
        
    except Exception as e:
        print(f"❌ Credit system test failed: {e}")

async def main():
    """Main function"""
    
    print("🚀 TELEGRAM AUTO-REACTION BOT")
    print("Phase 1 Implementation: Database Schema & Credit System")
    print("=" * 60)
    
    # Run Phase 1 setup
    success = await setup_phase1()
    
    if success:
        # Test credit system
        await test_credit_system()
        
        print("\n🎯 Phase 1 Implementation Complete!")
        print("Ready to proceed with Phase 2: Multi-Channel Management")
    else:
        print("\n💥 Phase 1 setup failed!")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Setup interrupted by user")
    except Exception as e:
        print(f"\n💥 Fatal error: {e}")
        sys.exit(1)
