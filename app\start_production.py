#!/usr/bin/env python3
"""
Production Startup Script for Credit-Based Multi-Channel Auto-Reaction Bot
"""

import os
import sys
import asyncio
import logging
from datetime import datetime

def setup_logging():
    """Setup production logging"""
    
    # Create logs directory if it doesn't exist
    os.makedirs('logs', exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f'logs/bot_{datetime.now().strftime("%Y%m%d")}.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def check_environment():
    """Check required environment variables"""

    # Load environment first
    from dotenv import load_dotenv
    load_dotenv(os.path.join('code', '.env'))

    required_vars = [
        'TELEGRAM_BOT_TOKEN',
        'TELEGRAM_API_ID',
        'TELEGRAM_API_HASH',
        'MONGODB_URL',
        'ADMIN_USER_IDS'
    ]

    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        print("Please check your .env file in the code/ directory")
        return False

    print("✅ Environment variables loaded")
    return True

def check_dependencies():
    """Check if required dependencies are installed"""
    
    try:
        import telegram
        import motor
        import pyrogram
        import dotenv
        print("✅ All required dependencies found")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Run: pip install -r code/requirements.txt")
        return False

async def main():
    """Main startup function"""

    print("🚀 CREDIT-BASED MULTI-CHANNEL AUTO-REACTION BOT")
    print("Production Startup - Complete System")
    print("=" * 60)

    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)

    # Check environment
    if not check_environment():
        sys.exit(1)

    # Check dependencies
    if not check_dependencies():
        sys.exit(1)

    # Environment already loaded in check_environment()

    print("✅ Environment loaded")
    print("✅ Dependencies verified")
    print("🚀 Starting complete production system...")
    print("=" * 60)

    # Add app directory to Python path
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

    # Import required components
    try:
        from auto_reaction_bot import CompleteCreditBasedAutoReactionBot
        from src.workers.enhanced_task_processor import EnhancedTaskProcessor
        from src.services.webhook_service import WebhookService

        print("📦 Imported all components")

        # Create database client
        from motor.motor_asyncio import AsyncIOMotorClient
        mongodb_url = os.getenv('MONGODB_URL')
        db_client = AsyncIOMotorClient(mongodb_url)

        print("🔗 Database client created")

        # Create components with proper initialization
        main_bot = CompleteCreditBasedAutoReactionBot()
        task_processor = EnhancedTaskProcessor(db_client)
        webhook_service = WebhookService(db_client)

        print("🔧 Created all service instances")

        # Start all components concurrently
        print("🚀 Starting all services...")

        # Create tasks for all services
        tasks = [
            asyncio.create_task(main_bot.run(), name="main_bot"),
            asyncio.create_task(task_processor.start_processing(), name="task_processor"),
            asyncio.create_task(webhook_service.start_background_services(), name="webhook_service")
        ]

        print("✅ Main Bot: Starting Telegram bot and long polling")
        print("✅ Task Processor: Starting userbot worker pool")
        print("✅ Webhook Service: Starting post processing service")
        print("=" * 60)
        print("🎯 SYSTEM OPERATIONAL - All services running")
        print("📊 Post Detection: Main bot long polling")
        print("🤖 Reaction Sending: Userbot worker pool")
        print("💰 Credit System: Pay-per-success model")
        print("=" * 60)

        # Wait for all tasks to complete (or one to fail)
        done, pending = await asyncio.wait(
            tasks,
            return_when=asyncio.FIRST_EXCEPTION
        )

        # If we get here, something failed
        for task in done:
            if task.exception():
                logger.error(f"Service {task.get_name()} failed: {task.exception()}")
                print(f"💥 Service {task.get_name()} failed: {task.exception()}")

        # Cancel remaining tasks
        for task in pending:
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass

    except KeyboardInterrupt:
        logger.info("System stopped by user")
        print("\n👋 System stopped by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        print(f"\n💥 Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Startup interrupted")
    except Exception as e:
        print(f"\n💥 Startup error: {e}")
        sys.exit(1)
