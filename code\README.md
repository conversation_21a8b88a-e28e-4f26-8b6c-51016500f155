# Telegram Auto-Reaction Bot

A sophisticated Telegram bot system that automatically sends reactions to channel posts without requiring channel membership. The system processes forwarded messages and sends reactions using a userbot worker, making it perfect for automated engagement and testing.

## 🚀 Features

- **No Channel Joining Required**: Send reactions to public channels without membership
- **Real-time Processing**: Reactions appear within seconds of selection
- **Multiple Reaction Options**: 👍, ❤️, 🔥, 😁, 🤩, 👏, 🎉 and more
- **Robust Error Handling**: Multiple fallback methods for reliable delivery
- **Complete Monitoring**: Health checks, statistics, and logging
- **Production Ready**: Clean, scalable architecture with Docker support

## 🏗️ System Architecture

The system consists of 4 core components:

### 1. Enhanced <PERSON><PERSON> (`enhanced_bot.py`)
- **Purpose**: Main Telegram bot interface (@multi_reactions_bot)
- **Functions**: Handles user interactions, processes forwarded messages, creates reaction tasks
- **Technology**: python-telegram-bot library

### 2. Userbot Worker (`fixed_userbot_worker.py`)
- **Purpose**: Sends reactions using authenticated Telegram user session
- **Functions**: Processes reaction tasks, handles API calls, manages session authentication
- **Technology**: Pyrogram library

### 3. FastAPI Server (`launch_api.py`)
- **Purpose**: REST API for monitoring and administration
- **Functions**: Health checks, statistics, user management
- **Endpoints**: `/health`, `/admin/stats`, user management APIs

### 4. Celery Worker (`launch_celery.py`)
- **Purpose**: Background task processing and queue management
- **Functions**: Handles asynchronous tasks, manages Redis queues
- **Technology**: Celery with Redis broker

### Supporting Services
- **MongoDB**: Database for users, sessions, tasks, and settings
- **Redis**: Task queue and caching
- **Docker**: Containerized database services

## 📋 Prerequisites

- **Python 3.8+**
- **Docker & Docker Compose**
- **Telegram API Credentials** (API ID, API Hash)
- **Telegram Bot Token**
- **Verified Telegram Account** (for userbot session)

## 🛠️ Local Development Setup

### 1. Clone Repository
```bash
git clone <repository-url>
cd auto-multi-reaction-bot
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Start Docker Services
```bash
docker-compose up -d mongodb redis
```

### 4. Environment Configuration
Create `.env` file in project root:
```env
# Telegram Configuration
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_API_ID=your_api_id
TELEGRAM_API_HASH=your_api_hash

# Database Configuration
MONGODB_URL=mongodb://localhost:27017/telegram_reaction_bot

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Admin Configuration
ADMIN_USER_IDS=*********,*********

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
```

### 5. Create Userbot Session
Run the session creation script and follow prompts:
```bash
python -c "
from pyrogram import Client
import os
from dotenv import load_dotenv
load_dotenv()

client = Client(
    'sessions/userbot',
    api_id=int(os.getenv('TELEGRAM_API_ID')),
    api_hash=os.getenv('TELEGRAM_API_HASH')
)
client.start()
print('Session created successfully!')
client.stop()
"
```

### 6. Start All Components
Open 4 separate terminals and run:

**Terminal 1 - FastAPI Server:**
```bash
python launch_api.py
```

**Terminal 2 - Celery Worker:**
```bash
python launch_celery.py
```

**Terminal 3 - Enhanced Bot:**
```bash
python enhanced_bot.py
```

**Terminal 4 - Userbot Worker:**
```bash
python fixed_userbot_worker.py
```

## 🎯 Usage Instructions

### Testing the Bot

1. **Start a chat** with @multi_reactions_bot on Telegram
2. **Send `/start`** to see the welcome message
3. **Forward a message** from any public channel to the bot
4. **Select a reaction** from the provided buttons
5. **Check the original post** - the reaction will appear!

### Supported Commands
- `/start` - Welcome message and instructions
- `/help` - Detailed help information
- `/status` - System status and statistics

### API Endpoints

#### Health Check
```bash
GET /health
Response: {"status":"healthy","service":"telegram-auto-reaction-bot-api","timestamp":123456}
```

#### System Statistics
```bash
GET /admin/stats
Response: {
  "status": "success",
  "data": {
    "users": 2,
    "channels": 0,
    "userbot_sessions": 1,
    "reaction_tasks": 8,
    "settings": 8
  }
}
```

## 🔧 Configuration Guide

### Environment Variables

| Variable | Description | Required | Example |
|----------|-------------|----------|---------|
| `TELEGRAM_BOT_TOKEN` | Bot token from @BotFather | Yes | `123456:ABC-DEF...` |
| `TELEGRAM_API_ID` | API ID from my.telegram.org | Yes | `12345678` |
| `TELEGRAM_API_HASH` | API Hash from my.telegram.org | Yes | `abcdef123456...` |
| `MONGODB_URL` | MongoDB connection string | Yes | `mongodb://localhost:27017/db` |
| `REDIS_URL` | Redis connection string | Yes | `redis://localhost:6379/0` |
| `ADMIN_USER_IDS` | Comma-separated admin user IDs | Yes | `*********,*********` |
| `API_HOST` | FastAPI server host | No | `0.0.0.0` |
| `API_PORT` | FastAPI server port | No | `8000` |

### Docker Services Configuration

The system uses Docker Compose for MongoDB and Redis:

```yaml
# docker-compose.yml
version: '3.8'
services:
  mongodb:
    image: mongo:7.0
    container_name: telegram_bot_mongodb
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      MONGO_INITDB_DATABASE: telegram_reaction_bot

  redis:
    image: redis:7.0-alpine
    container_name: telegram_bot_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  mongodb_data:
  redis_data:
```

## 🐛 Troubleshooting

### Common Issues

#### 1. "CHANNEL_INVALID" Error
**Cause**: Userbot session has authentication issues
**Solution**: 
- Recreate userbot session with verified account
- Ensure account has complete profile (not just ".")
- Check account restrictions

#### 2. Bot Not Responding
**Cause**: Bot token invalid or bot not started
**Solution**:
- Verify bot token in .env file
- Check if enhanced_bot.py is running
- Restart the bot component

#### 3. Reactions Not Appearing
**Cause**: Userbot worker not processing tasks
**Solution**:
- Check if fixed_userbot_worker.py is running
- Verify userbot session is authenticated
- Check Redis connection

#### 4. Database Connection Failed
**Cause**: MongoDB not accessible
**Solution**:
- Ensure Docker MongoDB container is running: `docker ps`
- Check MongoDB URL in .env file
- Restart MongoDB: `docker-compose restart mongodb`

#### 5. Task Queue Not Processing
**Cause**: Redis or Celery worker issues
**Solution**:
- Check Redis container: `docker exec telegram_bot_redis redis-cli ping`
- Restart Celery worker
- Verify Redis URL in .env file

### Logs and Debugging

Each component provides detailed logging:
- **Enhanced Bot**: User interactions and forwarded message processing
- **Userbot Worker**: Reaction sending attempts and API responses
- **FastAPI Server**: HTTP requests and health status
- **Celery Worker**: Background task processing

Monitor logs for error messages and use them to identify specific issues.

### Performance Optimization

- **Rate Limiting**: Telegram has rate limits for reactions (avoid spam)
- **Session Management**: Use verified accounts with complete profiles
- **Error Handling**: The system includes multiple fallback methods
- **Monitoring**: Use `/admin/stats` endpoint to track system performance

## 📊 System Requirements

### Minimum Requirements
- **CPU**: 1 core
- **RAM**: 512MB
- **Storage**: 1GB
- **Network**: Stable internet connection

### Recommended for Production
- **CPU**: 2+ cores
- **RAM**: 2GB+
- **Storage**: 5GB+
- **Network**: High-speed connection with low latency

## 🔒 Security Considerations

- Keep `.env` file secure and never commit to version control
- Use strong, unique passwords for database access
- Regularly update dependencies
- Monitor system logs for suspicious activity
- Implement proper firewall rules in production

## 📈 Monitoring

The system provides built-in monitoring through:
- Health check endpoint (`/health`)
- Statistics endpoint (`/admin/stats`)
- Detailed logging from all components
- Docker container health monitoring

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the troubleshooting section above
- Review system logs for error messages
- Ensure all prerequisites are met
- Verify configuration settings

## 🔄 System Workflow

### Complete Reaction Process
1. **User forwards** a channel message to @multi_reactions_bot
2. **Enhanced Bot** extracts channel ID, message ID, and displays reaction options
3. **User selects** a reaction from the inline keyboard
4. **Task Creation** - Bot creates a reaction task in MongoDB
5. **Queue Processing** - Task is added to Redis queue
6. **Userbot Worker** picks up the task and sends the reaction
7. **Confirmation** - User receives success/failure notification
8. **Monitoring** - Task status is updated in database

### Data Flow
```
Telegram User → Enhanced Bot → MongoDB (Task) → Redis (Queue) → Userbot Worker → Telegram API → Channel Post
```

## 🔧 Advanced Configuration

### Custom Reaction Sets
Modify reaction options in `enhanced_bot.py`:
```python
keyboard = [
    [
        InlineKeyboardButton("👍", callback_data=f"react:{channel_id}:{message_id}:👍"),
        InlineKeyboardButton("❤️", callback_data=f"react:{channel_id}:{message_id}:❤️"),
        InlineKeyboardButton("🔥", callback_data=f"react:{channel_id}:{message_id}:🔥"),
    ],
    # Add more reaction rows here
]
```

### Rate Limiting Configuration
Adjust rate limits in userbot worker:
```python
# Add delays between reactions
await asyncio.sleep(1)  # 1 second delay
```

### Database Indexing
For better performance with large datasets:
```javascript
// MongoDB indexes
db.reaction_tasks.createIndex({"status": 1, "created_at": -1})
db.userbot_sessions.createIndex({"session_name": 1})
db.users.createIndex({"telegram_user_id": 1})
```

## 📱 Mobile Development Setup

### Quick Start Script
Create `start.sh` for easy development:
```bash
#!/bin/bash
echo "Starting Telegram Auto-Reaction Bot..."

# Start Docker services
docker-compose up -d mongodb redis

# Wait for services to be ready
sleep 5

# Start all components in background
python launch_api.py &
python launch_celery.py &
python enhanced_bot.py &
python fixed_userbot_worker.py &

echo "All services started! Check logs for status."
echo "API: http://localhost:8000/health"
echo "Bot: @multi_reactions_bot"
```

### Stop Script
Create `stop.sh`:
```bash
#!/bin/bash
echo "Stopping Telegram Auto-Reaction Bot..."

# Kill Python processes
pkill -f "python.*launch_api.py"
pkill -f "python.*launch_celery.py"
pkill -f "python.*enhanced_bot.py"
pkill -f "python.*fixed_userbot_worker.py"

# Stop Docker services
docker-compose down

echo "All services stopped."
```

## 🧪 Testing and Development

### Unit Testing
```bash
# Install test dependencies
pip install pytest pytest-asyncio

# Run tests (when test files are created)
pytest tests/
```

### Load Testing
```bash
# Test API endpoints
curl -X GET http://localhost:8000/health
curl -X GET http://localhost:8000/admin/stats

# Test reaction processing
# Forward multiple messages quickly to test queue processing
```

### Development Environment
```bash
# Set development environment
export ENVIRONMENT=development
export LOG_LEVEL=DEBUG

# Enable debug logging
python enhanced_bot.py  # Will show detailed logs
```

## 🌐 Production Deployment

For production deployment on a Linux VPS, see the comprehensive [DEPLOYMENT_GUIDE.md](DEPLOYMENT_GUIDE.md) which includes:

- VPS setup and requirements
- Docker installation and configuration
- SSL/HTTPS setup with Nginx
- Process management with systemd
- Security hardening
- Monitoring and logging
- Backup procedures
- Troubleshooting guide

## 📊 Performance Metrics

### Expected Performance
- **Reaction Speed**: 1-3 seconds from selection to appearance
- **Throughput**: 10-50 reactions per minute (depending on rate limits)
- **Uptime**: 99.9% with proper monitoring
- **Resource Usage**: ~100MB RAM per component

### Scaling Considerations
- **Horizontal Scaling**: Run multiple userbot workers
- **Database Optimization**: Use MongoDB replica sets
- **Caching**: Implement Redis caching for frequent queries
- **Load Balancing**: Use Nginx for API load balancing

## 🔍 Monitoring and Analytics

### Key Metrics to Monitor
- Reaction success rate
- Average processing time
- Queue length and processing speed
- Error rates by component
- Database performance
- API response times

### Alerting Setup
Configure alerts for:
- Service downtime
- High error rates
- Queue backlog
- Database connection issues
- Disk space usage

---

**The Telegram Auto-Reaction Bot is ready for production use! 🚀**

For deployment instructions, see [DEPLOYMENT_GUIDE.md](DEPLOYMENT_GUIDE.md)
