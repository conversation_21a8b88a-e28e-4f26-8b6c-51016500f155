#!/usr/bin/env python3
"""
Channel Service - Multi-Channel Management System
"""

import asyncio
import secrets
from typing import Optional, List, Dict, Any, Tuple
from motor.motor_asyncio import AsyncIOMotorClient
from datetime import datetime, timezone
from bson import ObjectId
import logging

logger = logging.getLogger(__name__)

class ChannelService:
    """Service for managing channels and their settings"""
    
    def __init__(self, db_client: AsyncIOMotorClient):
        self.client = db_client
        self.db = db_client.telegram_reaction_bot
        
    async def add_channel(
        self, 
        channel_id: int,
        channel_username: Optional[str],
        channel_title: str,
        owner_user_id: int,
        added_by_user_id: int
    ) -> Tuple[bool, str, Optional[str]]:
        """Add a new channel with verification token"""
        
        try:
            # Check if channel already exists
            existing = await self.db.channels.find_one({"channel_id": channel_id})
            if existing:
                return False, "Channel already exists", None
            
            # Check user's channel limit
            user = await self.db.users.find_one({"telegram_user_id": owner_user_id})
            if not user:
                return False, "User not found", None
            
            user_channels = await self.db.channels.count_documents({
                "owner_user_id": owner_user_id,
                "is_active": True
            })
            
            max_channels = user.get("max_channels", 1)
            if user_channels >= max_channels:
                return False, f"Channel limit reached ({max_channels})", None
            
            # Generate verification token
            verification_token = secrets.token_urlsafe(16)
            
            # Create channel document
            channel_doc = {
                "channel_id": channel_id,
                "channel_username": channel_username,
                "channel_title": channel_title,
                "owner_user_id": owner_user_id,
                "added_by_user_id": added_by_user_id,
                "verification_status": "pending",
                "verification_token": verification_token,
                "is_active": False,  # Inactive until verified
                "reactions_per_post": 1,
                "emoji_list": ["👍", "❤️", "🔥"],
                "reaction_mode": "random",
                "reaction_delay_min": 5,
                "reaction_delay_max": 30,
                "auto_reaction_enabled": False,  # Auto-reaction toggle
                "total_posts_processed": 0,
                "total_reactions_sent": 0,
                "total_credits_consumed": 0,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            }
            
            result = await self.db.channels.insert_one(channel_doc)
            logger.info(f"Channel {channel_id} added by user {owner_user_id}")
            return True, str(result.inserted_id), verification_token
            
        except Exception as e:
            logger.error(f"Error adding channel {channel_id}: {e}")
            return False, str(e), None

    async def add_channel_with_auto_verification(
        self,
        channel_id: int,
        channel_username: Optional[str],
        channel_title: str,
        owner_user_id: int,
        added_by_user_id: int,
        bot_instance=None
    ) -> Tuple[bool, str, Optional[str]]:
        """Add and automatically verify a channel if bot has admin permissions"""

        try:
            # Check if channel already exists
            existing = await self.db.channels.find_one({"channel_id": channel_id})
            if existing:
                if existing["owner_user_id"] == owner_user_id:
                    return False, "Channel already added to your account", None
                else:
                    return False, "Channel is already managed by another user", None

            # Check user's channel limit
            user = await self.db.users.find_one({"telegram_user_id": owner_user_id})
            if not user:
                return False, "User not found", None

            user_channels = await self.db.channels.count_documents({
                "owner_user_id": owner_user_id,
                "is_active": True
            })

            max_channels = user.get("max_channels", 1)
            if user_channels >= max_channels:
                return False, f"Channel limit reached ({max_channels}). Upgrade your subscription for more channels.", None

            # Check bot admin permissions
            admin_status, admin_message = await self.check_bot_admin_permissions(channel_id, bot_instance)

            if not admin_status:
                return False, admin_message, None

            # Generate verification token
            verification_token = secrets.token_urlsafe(16)

            # Create channel document with automatic verification
            channel_doc = {
                "channel_id": channel_id,
                "channel_username": channel_username,
                "channel_title": channel_title,
                "owner_user_id": owner_user_id,
                "added_by_user_id": added_by_user_id,
                "verification_status": "verified",  # Auto-verified
                "verification_token": verification_token,
                "is_active": True,  # Auto-activated
                "reactions_per_post": 1,
                "emoji_list": ["👍", "❤️", "🔥"],
                "reaction_mode": "random",
                "reaction_delay_min": 5,
                "reaction_delay_max": 30,
                "auto_reaction_enabled": True,  # Auto-enabled
                "total_posts_processed": 0,
                "total_reactions_sent": 0,
                "total_credits_consumed": 0,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "verified_at": datetime.now(timezone.utc)
            }

            result = await self.db.channels.insert_one(channel_doc)
            logger.info(f"Channel {channel_id} auto-verified and added by user {owner_user_id}")
            return True, "Channel added and verified successfully! Auto-reactions are now enabled.", verification_token

        except Exception as e:
            logger.error(f"Error adding channel with auto-verification: {e}")
            return False, f"Error adding channel: {e}", None

    async def check_bot_admin_permissions(self, channel_id: int, bot_instance=None) -> Tuple[bool, str]:
        """Check if bot has admin permissions in the channel"""

        try:
            if not bot_instance:
                return False, "Bot instance not available for permission check"

            # Get bot information
            bot_info = await bot_instance.application.bot.get_me()
            bot_id = bot_info.id

            # Try to get chat member info for the bot
            try:
                chat_member = await bot_instance.application.bot.get_chat_member(channel_id, bot_id)

                if chat_member.status in ['administrator', 'creator']:
                    # Check specific permissions
                    if hasattr(chat_member, 'can_post_messages') and not chat_member.can_post_messages:
                        return False, ("Bot is admin but lacks required permissions.\n\n"
                                     "Please ensure the bot has these permissions:\n"
                                     "• Post messages\n"
                                     "• Read message history\n"
                                     "• Add reactions")

                    return True, "Bot has admin permissions"
                else:
                    return False, ("Bot is not admin in this channel.\n\n"
                                 "Please make this bot an administrator with these permissions:\n"
                                 "• Post messages\n"
                                 "• Read message history\n"
                                 "• Add reactions\n\n"
                                 "Then try forwarding a message again.")

            except Exception as perm_error:
                logger.error(f"Error checking permissions for channel {channel_id}: {perm_error}")
                return False, ("Unable to verify bot permissions.\n\n"
                             "Please ensure:\n"
                             "1. Bot is added as admin in the channel\n"
                             "2. Bot has required permissions\n"
                             "3. Channel is accessible\n\n"
                             "Then try again.")

        except Exception as e:
            logger.error(f"Error checking bot admin permissions: {e}")
            return False, f"Permission check failed: {e}"

    async def verify_channel(self, channel_id: int, verification_token: str) -> Tuple[bool, str]:
        """Verify channel ownership using token"""
        
        try:
            channel = await self.db.channels.find_one({
                "channel_id": channel_id,
                "verification_token": verification_token,
                "verification_status": "pending"
            })
            
            if not channel:
                return False, "Invalid verification token or channel not found"
            
            # Update channel status
            await self.db.channels.update_one(
                {"_id": channel["_id"]},
                {
                    "$set": {
                        "verification_status": "verified",
                        "is_active": True,
                        "updated_at": datetime.now(timezone.utc)
                    },
                    "$unset": {
                        "verification_token": ""
                    }
                }
            )
            
            logger.info(f"Channel {channel_id} verified successfully")
            return True, "Channel verified successfully"
            
        except Exception as e:
            logger.error(f"Error verifying channel {channel_id}: {e}")
            return False, str(e)
    
    async def get_user_channels(self, user_id: int) -> List[Dict[str, Any]]:
        """Get all channels owned by user"""

        try:
            cursor = self.db.channels.find({
                "owner_user_id": user_id
            }).sort("created_at", -1)

            return await cursor.to_list(length=None)
        except Exception as e:
            logger.error(f"Error getting channels for user {user_id}: {e}")
            return []

    async def update_channel_settings(
        self,
        channel_id: int,
        user_id: int,
        settings: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Update channel settings"""

        try:
            # Verify channel ownership
            channel = await self.db.channels.find_one({
                "channel_id": channel_id,
                "owner_user_id": user_id
            })

            if not channel:
                return False, "Channel not found or access denied"

            # Validate settings
            valid_settings = {}

            if "auto_reaction_enabled" in settings:
                valid_settings["auto_reaction_enabled"] = bool(settings["auto_reaction_enabled"])

            if "emoji_list" in settings:
                emoji_list = settings["emoji_list"]
                if isinstance(emoji_list, list) and len(emoji_list) > 0:
                    valid_settings["emoji_list"] = emoji_list[:10]  # Limit to 10 emojis

            if "reactions_per_post" in settings:
                reactions = int(settings["reactions_per_post"])
                if 1 <= reactions <= 10:
                    valid_settings["reactions_per_post"] = reactions

            if "reaction_delay_min" in settings:
                delay_min = int(settings["reaction_delay_min"])
                if 1 <= delay_min <= 300:  # 1 second to 5 minutes
                    valid_settings["reaction_delay_min"] = delay_min

            if "reaction_delay_max" in settings:
                delay_max = int(settings["reaction_delay_max"])
                if 1 <= delay_max <= 300:
                    valid_settings["reaction_delay_max"] = delay_max

            if "reaction_mode" in settings:
                mode = settings["reaction_mode"]
                if mode in ["random", "sequential", "fixed"]:
                    valid_settings["reaction_mode"] = mode

            # Ensure min <= max for delays
            if "reaction_delay_min" in valid_settings and "reaction_delay_max" in valid_settings:
                if valid_settings["reaction_delay_min"] > valid_settings["reaction_delay_max"]:
                    valid_settings["reaction_delay_max"] = valid_settings["reaction_delay_min"]

            if not valid_settings:
                return False, "No valid settings provided"

            # Update the channel
            valid_settings["updated_at"] = datetime.now(timezone.utc)

            result = await self.db.channels.update_one(
                {"channel_id": channel_id, "owner_user_id": user_id},
                {"$set": valid_settings}
            )

            if result.modified_count > 0:
                logger.info(f"Channel {channel_id} settings updated by user {user_id}")
                return True, "Settings updated successfully"
            else:
                return False, "No changes made"

        except Exception as e:
            logger.error(f"Error updating channel settings: {e}")
            return False, f"Error updating settings: {e}"

    async def toggle_channel_status(self, channel_id: int, user_id: int) -> Tuple[bool, str]:
        """Toggle channel active status"""

        try:
            channel = await self.db.channels.find_one({
                "channel_id": channel_id,
                "owner_user_id": user_id
            })

            if not channel:
                return False, "Channel not found or access denied"

            new_status = not channel.get("is_active", False)

            result = await self.db.channels.update_one(
                {"channel_id": channel_id, "owner_user_id": user_id},
                {
                    "$set": {
                        "is_active": new_status,
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )

            if result.modified_count > 0:
                status_text = "activated" if new_status else "paused"
                logger.info(f"Channel {channel_id} {status_text} by user {user_id}")
                return True, f"Channel {status_text} successfully"
            else:
                return False, "Failed to update channel status"

        except Exception as e:
            logger.error(f"Error toggling channel status: {e}")
            return False, f"Error updating status: {e}"

    async def remove_channel(self, channel_id: int, user_id: int) -> Tuple[bool, str]:
        """Remove a channel and clean up related data"""

        try:
            # Verify channel ownership
            channel = await self.db.channels.find_one({
                "channel_id": channel_id,
                "owner_user_id": user_id
            })

            if not channel:
                return False, "Channel not found or access denied"

            # Remove related data
            await self.db.reaction_tasks.delete_many({"channel_id": channel_id})
            await self.db.channel_posts.delete_many({"channel_id": channel_id})

            # Remove the channel
            result = await self.db.channels.delete_one({
                "channel_id": channel_id,
                "owner_user_id": user_id
            })

            if result.deleted_count > 0:
                logger.info(f"Channel {channel_id} removed by user {user_id}")
                return True, "Channel removed successfully"
            else:
                return False, "Failed to remove channel"

        except Exception as e:
            logger.error(f"Error removing channel: {e}")
            return False, f"Error removing channel: {e}"

    async def get_channel_statistics(self, channel_id: int, user_id: int) -> Dict[str, Any]:
        """Get detailed statistics for a channel"""

        try:
            # Verify channel ownership
            channel = await self.db.channels.find_one({
                "channel_id": channel_id,
                "owner_user_id": user_id
            })

            if not channel:
                return {}

            # Get time-based statistics
            now = datetime.now(timezone.utc)
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            week_start = today_start - timedelta(days=7)
            month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

            # Get reaction statistics
            total_reactions = await self.db.reaction_tasks.count_documents({
                "channel_id": channel_id
            })

            successful_reactions = await self.db.reaction_tasks.count_documents({
                "channel_id": channel_id,
                "status": "completed"
            })

            today_reactions = await self.db.reaction_tasks.count_documents({
                "channel_id": channel_id,
                "created_at": {"$gte": today_start}
            })

            week_reactions = await self.db.reaction_tasks.count_documents({
                "channel_id": channel_id,
                "created_at": {"$gte": week_start}
            })

            month_reactions = await self.db.reaction_tasks.count_documents({
                "channel_id": channel_id,
                "created_at": {"$gte": month_start}
            })

            # Get post statistics
            total_posts = await self.db.channel_posts.count_documents({
                "channel_id": channel_id
            })

            today_posts = await self.db.channel_posts.count_documents({
                "channel_id": channel_id,
                "discovered_at": {"$gte": today_start}
            })

            # Calculate success rate
            success_rate = (successful_reactions / total_reactions * 100) if total_reactions > 0 else 0

            return {
                "channel_info": {
                    "title": channel.get("channel_title", "Unknown"),
                    "username": channel.get("channel_username", ""),
                    "is_active": channel.get("is_active", False),
                    "auto_reaction_enabled": channel.get("auto_reaction_enabled", False),
                    "created_at": channel.get("created_at"),
                    "verified_at": channel.get("verified_at")
                },
                "reaction_stats": {
                    "total_reactions": total_reactions,
                    "successful_reactions": successful_reactions,
                    "success_rate": success_rate,
                    "today_reactions": today_reactions,
                    "week_reactions": week_reactions,
                    "month_reactions": month_reactions
                },
                "post_stats": {
                    "total_posts": total_posts,
                    "today_posts": today_posts,
                    "avg_reactions_per_post": total_reactions / total_posts if total_posts > 0 else 0
                },
                "settings": {
                    "reactions_per_post": channel.get("reactions_per_post", 1),
                    "emoji_list": channel.get("emoji_list", []),
                    "reaction_mode": channel.get("reaction_mode", "random"),
                    "reaction_delay_min": channel.get("reaction_delay_min", 5),
                    "reaction_delay_max": channel.get("reaction_delay_max", 30)
                }
            }

        except Exception as e:
            logger.error(f"Error getting channel statistics: {e}")
            return {}
    
    async def update_channel_settings(
        self, 
        channel_id: int, 
        user_id: int, 
        settings: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Update channel settings"""
        
        try:
            # Verify ownership
            channel = await self.db.channels.find_one({
                "channel_id": channel_id,
                "owner_user_id": user_id
            })
            
            if not channel:
                return False, "Channel not found or access denied"
            
            # Validate settings
            allowed_settings = {
                "reactions_per_post", "emoji_list", "reaction_mode",
                "reaction_delay_min", "reaction_delay_max", "is_active"
            }
            
            update_data = {}
            for key, value in settings.items():
                if key in allowed_settings:
                    # Validate specific settings
                    if key == "reactions_per_post" and (not isinstance(value, int) or value < 1 or value > 10):
                        return False, "reactions_per_post must be between 1 and 10"
                    if key == "emoji_list" and (not isinstance(value, list) or len(value) == 0):
                        return False, "emoji_list must be a non-empty list"
                    if key == "reaction_mode" and value not in ["random", "fixed", "sequential"]:
                        return False, "reaction_mode must be random, fixed, or sequential"
                    if key in ["reaction_delay_min", "reaction_delay_max"] and (not isinstance(value, int) or value < 1):
                        return False, f"{key} must be a positive integer"
                    
                    update_data[key] = value
            
            if not update_data:
                return False, "No valid settings provided"
            
            update_data["updated_at"] = datetime.now(timezone.utc)
            
            await self.db.channels.update_one(
                {"channel_id": channel_id},
                {"$set": update_data}
            )
            
            logger.info(f"Channel {channel_id} settings updated by user {user_id}")
            return True, "Settings updated successfully"
            
        except Exception as e:
            logger.error(f"Error updating channel {channel_id} settings: {e}")
            return False, str(e)
    
    async def pause_channel(self, channel_id: int, user_id: int) -> Tuple[bool, str]:
        """Pause channel reactions"""
        return await self.update_channel_settings(
            channel_id, user_id, {"is_active": False}
        )
    
    async def resume_channel(self, channel_id: int, user_id: int) -> Tuple[bool, str]:
        """Resume channel reactions"""
        return await self.update_channel_settings(
            channel_id, user_id, {"is_active": True}
        )
    
    async def remove_channel(self, channel_id: int, user_id: int) -> Tuple[bool, str]:
        """Remove channel (soft delete)"""
        
        try:
            # Verify ownership
            channel = await self.db.channels.find_one({
                "channel_id": channel_id,
                "owner_user_id": user_id
            })
            
            if not channel:
                return False, "Channel not found or access denied"
            
            # Cancel pending tasks
            await self.db.reaction_tasks.update_many(
                {
                    "channel_id": channel_id,
                    "status": "pending"
                },
                {
                    "$set": {
                        "status": "cancelled",
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            
            # Remove channel
            await self.db.channels.delete_one({"_id": channel["_id"]})
            
            logger.info(f"Channel {channel_id} removed by user {user_id}")
            return True, "Channel removed successfully"
            
        except Exception as e:
            logger.error(f"Error removing channel {channel_id}: {e}")
            return False, str(e)
    
    async def get_channel_statistics(self, channel_id: int, user_id: int) -> Optional[Dict[str, Any]]:
        """Get detailed channel statistics"""
        
        try:
            channel = await self.db.channels.find_one({
                "channel_id": channel_id,
                "owner_user_id": user_id
            })
            
            if not channel:
                return None
            
            # Get recent tasks
            recent_tasks = await self.db.reaction_tasks.find({
                "channel_id": channel_id
            }).sort("created_at", -1).limit(10).to_list(length=10)
            
            # Calculate success rate
            total_tasks = await self.db.reaction_tasks.count_documents({
                "channel_id": channel_id
            })
            
            successful_tasks = await self.db.reaction_tasks.count_documents({
                "channel_id": channel_id,
                "status": "completed"
            })
            
            success_rate = (successful_tasks / total_tasks * 100) if total_tasks > 0 else 0
            
            # Get daily statistics
            today_start = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
            
            today_tasks = await self.db.reaction_tasks.count_documents({
                "channel_id": channel_id,
                "created_at": {"$gte": today_start}
            })
            
            today_successful = await self.db.reaction_tasks.count_documents({
                "channel_id": channel_id,
                "status": "completed",
                "created_at": {"$gte": today_start}
            })
            
            return {
                "channel_info": channel,
                "total_tasks": total_tasks,
                "successful_tasks": successful_tasks,
                "success_rate": round(success_rate, 2),
                "today_tasks": today_tasks,
                "today_successful": today_successful,
                "recent_tasks": recent_tasks
            }
            
        except Exception as e:
            logger.error(f"Error getting statistics for channel {channel_id}: {e}")
            return None
    
    async def get_active_channels(self) -> List[Dict[str, Any]]:
        """Get all active channels for processing"""
        
        try:
            cursor = self.db.channels.find({
                "is_active": True,
                "verification_status": "verified"
            }).sort("created_at", 1)
            
            return await cursor.to_list(length=None)
        except Exception as e:
            logger.error(f"Error getting active channels: {e}")
            return []
    
    async def get_channel_by_id(self, channel_id: int) -> Optional[Dict[str, Any]]:
        """Get channel by ID"""
        
        try:
            return await self.db.channels.find_one({"channel_id": channel_id})
        except Exception as e:
            logger.error(f"Error getting channel {channel_id}: {e}")
            return None
