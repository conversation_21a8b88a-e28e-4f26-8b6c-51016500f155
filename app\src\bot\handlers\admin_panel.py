#!/usr/bin/env python3
"""
Admin Control Panel - Phase 5 Implementation
"""

import os
import sys
import asyncio
from typing import Dict, Any, List
from datetime import datetime, timezone, timedelta
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from motor.motor_asyncio import AsyncIOMotorClient
from bson import ObjectId

# Add src to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, '..', '..')
sys.path.insert(0, src_dir)

from services.credit_service import CreditService
from services.userbot_manager import UserbotManager
from services.webhook_service import WebhookService

class AdminPanelHandler:
    """Comprehensive admin control panel for system management"""
    
    def __init__(self, db_client: AsyncIOMotorClient):
        self.db = db_client.telegram_reaction_bot
        self.credit_service = CreditService(db_client)
        self.userbot_manager = UserbotManager(db_client)
        self.webhook_service = WebhookService(db_client)
        self.admin_user_ids = [int(x) for x in os.getenv('ADMIN_USER_IDS', '').split(',') if x]
    
    async def show_admin_panel(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show main admin control panel"""
        
        user_id = update.effective_user.id
        
        if user_id not in self.admin_user_ids:
            await update.message.reply_text("❌ Access denied. Admin privileges required.")
            return
        
        # Get comprehensive system statistics
        stats = await self.get_system_overview()
        
        text = f"""
🔧 **Admin Control Panel - Phase 5**

**System Overview:**
👥 Users: {stats['active_users']}/{stats['total_users']} active
📺 Channels: {stats['active_channels']}/{stats['total_channels']} active
🤖 Sessions: {stats['healthy_sessions']}/{stats['total_sessions']} healthy
💰 Credits: {stats['total_credits']:,} in system

**Today's Performance:**
📊 Tasks: {stats['today_tasks']} ({stats['success_rate']:.1f}% success)
🎭 Reactions: {stats['daily_reactions']}
📡 Posts Processed: {stats['posts_processed_today']}
🔔 Notifications: {stats['pending_notifications']} pending

**System Health:**
{"🟢 All Systems Operational" if stats['system_health'] == 'healthy' else "⚠️ Performance Issues" if stats['system_health'] == 'warning' else "🔴 Critical Issues"}

**Quick Actions:**
"""
        
        keyboard = [
            [
                InlineKeyboardButton("👥 User Management", callback_data="admin:users"),
                InlineKeyboardButton("🤖 Session Management", callback_data="admin:sessions")
            ],
            [
                InlineKeyboardButton("💰 Credit Management", callback_data="admin:credits"),
                InlineKeyboardButton("📊 System Analytics", callback_data="admin:analytics")
            ],
            [
                InlineKeyboardButton("📺 Channel Management", callback_data="admin:channels"),
                InlineKeyboardButton("🔔 Notifications", callback_data="admin:notifications")
            ],
            [
                InlineKeyboardButton("⚙️ System Settings", callback_data="admin:settings"),
                InlineKeyboardButton("🔄 Maintenance", callback_data="admin:maintenance")
            ],
            [
                InlineKeyboardButton("📈 Live Dashboard", callback_data="admin:dashboard"),
                InlineKeyboardButton("🚨 Emergency Controls", callback_data="admin:emergency")
            ]
        ]
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        if update.callback_query:
            await update.callback_query.edit_message_text(
                text,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
        else:
            await update.message.reply_text(
                text,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
    
    async def get_system_overview(self) -> Dict[str, Any]:
        """Get comprehensive system overview statistics"""
        
        # User statistics
        total_users = await self.db.users.count_documents({})
        active_users = await self.db.users.count_documents({"is_active": True})
        
        # Channel statistics
        total_channels = await self.db.channels.count_documents({})
        active_channels = await self.db.channels.count_documents({"is_active": True})
        
        # Session statistics
        session_stats = await self.userbot_manager.get_session_statistics()
        
        # Webhook statistics
        webhook_stats = await self.webhook_service.get_processing_statistics()
        
        # Task statistics
        today_start = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        today_tasks = await self.db.reaction_tasks.count_documents({"created_at": {"$gte": today_start}})
        today_successful = await self.db.reaction_tasks.count_documents({
            "created_at": {"$gte": today_start},
            "status": "completed"
        })
        
        success_rate = (today_successful / today_tasks * 100) if today_tasks > 0 else 0
        
        # Credit statistics
        credit_stats = await self.credit_service.get_system_credit_stats()
        
        # Determine system health
        system_health = 'healthy'
        if session_stats['healthy_sessions'] == 0:
            system_health = 'critical'
        elif session_stats['healthy_sessions'] < 3 or success_rate < 80:
            system_health = 'warning'
        
        return {
            'total_users': total_users,
            'active_users': active_users,
            'total_channels': total_channels,
            'active_channels': active_channels,
            'total_sessions': session_stats['total_sessions'],
            'healthy_sessions': session_stats['healthy_sessions'],
            'today_tasks': today_tasks,
            'success_rate': success_rate,
            'daily_reactions': session_stats['daily_reactions'],
            'posts_processed_today': webhook_stats.get('posts_processed_today', 0),
            'pending_notifications': webhook_stats.get('pending_notifications', 0),
            'total_credits': credit_stats.get('total_credits_in_system', 0),
            'system_health': system_health
        }
    
    async def show_user_management(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show user management panel"""
        
        query = update.callback_query
        
        # Get user statistics
        total_users = await self.db.users.count_documents({})
        active_users = await self.db.users.count_documents({"is_active": True})
        banned_users = await self.db.users.count_documents({"is_banned": True})
        
        # Get subscription tier breakdown
        tier_stats = await self.db.users.aggregate([
            {"$group": {"_id": "$subscription_tier", "count": {"$sum": 1}}}
        ]).to_list(length=None)
        
        tier_breakdown = {tier['_id']: tier['count'] for tier in tier_stats}
        
        # Get recent registrations
        week_ago = datetime.now(timezone.utc) - timedelta(days=7)
        recent_registrations = await self.db.users.count_documents({
            "created_at": {"$gte": week_ago}
        })
        
        text = f"""
👥 **User Management**

**Overview:**
📊 Total Users: {total_users}
✅ Active Users: {active_users}
🚫 Banned Users: {banned_users}
📈 New This Week: {recent_registrations}

**Subscription Tiers:**
🆓 Free: {tier_breakdown.get('free', 0)}
🥉 Basic: {tier_breakdown.get('basic', 0)}
🥈 Premium: {tier_breakdown.get('premium', 0)}
🥇 Enterprise: {tier_breakdown.get('enterprise', 0)}

**Management Actions:**
"""
        
        keyboard = [
            [
                InlineKeyboardButton("🔍 Search User", callback_data="admin:user_search"),
                InlineKeyboardButton("📊 User Analytics", callback_data="admin:user_analytics")
            ],
            [
                InlineKeyboardButton("💰 Bulk Credit Operations", callback_data="admin:bulk_credits"),
                InlineKeyboardButton("🚫 Moderation Tools", callback_data="admin:moderation")
            ],
            [
                InlineKeyboardButton("📈 Subscription Management", callback_data="admin:subscriptions"),
                InlineKeyboardButton("📧 User Communications", callback_data="admin:communications")
            ],
            [
                InlineKeyboardButton("🔙 Back to Admin Panel", callback_data="admin:main")
            ]
        ]
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
    
    async def show_session_management(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show session management panel"""
        
        query = update.callback_query
        
        # Get detailed session statistics
        session_stats = await self.userbot_manager.get_session_statistics()
        
        # Get session performance data
        top_sessions = await self.db.userbot_sessions.find({
            "is_active": True
        }).sort("reactions_sent_total", -1).limit(5).to_list(length=5)
        
        # Get problematic sessions
        problematic_sessions = await self.db.userbot_sessions.find({
            "is_active": True,
            "$or": [
                {"is_healthy": False},
                {"errors_count": {"$gte": 5}}
            ]
        }).limit(5).to_list(length=5)
        
        text = f"""
🤖 **Session Management**

**Overview:**
📊 Total Sessions: {session_stats['total_sessions']}
✅ Active Sessions: {session_stats['active_sessions']}
💚 Healthy Sessions: {session_stats['healthy_sessions']}
🔄 Loaded Sessions: {session_stats['loaded_sessions']}
⏸️ Flood Wait: {session_stats.get('flood_wait_sessions', 0)}

**Performance:**
🎭 Daily Reactions: {session_stats['daily_reactions']}
📈 Top Performer: {top_sessions[0]['session_name'] if top_sessions else 'None'} ({top_sessions[0]['reactions_sent_total'] if top_sessions else 0} total)

**Issues:**
⚠️ Problematic Sessions: {len(problematic_sessions)}

**Management Actions:**
"""
        
        keyboard = [
            [
                InlineKeyboardButton("📊 Session Analytics", callback_data="admin:session_analytics"),
                InlineKeyboardButton("🔄 Health Check All", callback_data="admin:health_check")
            ],
            [
                InlineKeyboardButton("➕ Add Session", callback_data="admin:add_session"),
                InlineKeyboardButton("🗑️ Remove Session", callback_data="admin:remove_session")
            ],
            [
                InlineKeyboardButton("⚙️ Session Settings", callback_data="admin:session_settings"),
                InlineKeyboardButton("🔧 Maintenance Mode", callback_data="admin:maintenance_mode")
            ],
            [
                InlineKeyboardButton("🔙 Back to Admin Panel", callback_data="admin:main")
            ]
        ]
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
    
    async def show_credit_management(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show credit management panel"""
        
        query = update.callback_query
        
        # Get credit statistics
        credit_stats = await self.credit_service.get_system_credit_stats()
        
        # Get top credit holders
        top_holders = await self.db.users.find({}).sort("credit_balance", -1).limit(5).to_list(length=5)
        
        # Get recent transactions
        recent_transactions = await self.db.credit_transactions.find({}).sort("created_at", -1).limit(10).to_list(length=10)
        
        # Calculate daily credit flow
        today_start = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        
        credits_added_today = await self.db.credit_transactions.aggregate([
            {
                "$match": {
                    "transaction_type": {"$in": ["purchase", "bonus"]},
                    "created_at": {"$gte": today_start}
                }
            },
            {"$group": {"_id": None, "total": {"$sum": "$amount"}}}
        ]).to_list(length=1)
        
        credits_spent_today = await self.db.credit_transactions.aggregate([
            {
                "$match": {
                    "transaction_type": "consumption",
                    "created_at": {"$gte": today_start}
                }
            },
            {"$group": {"_id": None, "total": {"$sum": {"$abs": "$amount"}}}}
        ]).to_list(length=1)
        
        text = f"""
💰 **Credit Management**

**System Overview:**
💰 Total Credits: {credit_stats.get('total_credits_in_system', 0):,}
📊 Today's Transactions: {credit_stats.get('today_transactions', 0)}
➕ Credits Added Today: {credits_added_today[0]['total'] if credits_added_today else 0:,}
➖ Credits Spent Today: {credits_spent_today[0]['total'] if credits_spent_today else 0:,}

**Top Credit Holders:**
{chr(10).join([f"👤 {user['first_name']}: {user['credit_balance']:,} credits" for user in top_holders[:3]])}

**Recent Activity:**
📈 {len(recent_transactions)} recent transactions

**Management Actions:**
"""
        
        keyboard = [
            [
                InlineKeyboardButton("➕ Add Credits", callback_data="admin:add_credits"),
                InlineKeyboardButton("📊 Credit Analytics", callback_data="admin:credit_analytics")
            ],
            [
                InlineKeyboardButton("🔍 Transaction Search", callback_data="admin:transaction_search"),
                InlineKeyboardButton("💳 Payment Management", callback_data="admin:payments")
            ],
            [
                InlineKeyboardButton("🎁 Bulk Bonuses", callback_data="admin:bulk_bonuses"),
                InlineKeyboardButton("📈 Revenue Reports", callback_data="admin:revenue")
            ],
            [
                InlineKeyboardButton("🔙 Back to Admin Panel", callback_data="admin:main")
            ]
        ]
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
    
    async def show_live_dashboard(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show live system dashboard"""
        
        query = update.callback_query
        
        # Get real-time statistics
        stats = await self.get_system_overview()
        
        # Get recent activity
        recent_tasks = await self.db.reaction_tasks.find({}).sort("created_at", -1).limit(5).to_list(length=5)
        recent_posts = await self.db.channel_posts.find({}).sort("discovered_at", -1).limit(5).to_list(length=5)
        
        # Get system performance metrics
        now = datetime.now(timezone.utc)
        hour_ago = now - timedelta(hours=1)
        
        tasks_last_hour = await self.db.reaction_tasks.count_documents({
            "created_at": {"$gte": hour_ago}
        })
        
        successful_last_hour = await self.db.reaction_tasks.count_documents({
            "created_at": {"$gte": hour_ago},
            "status": "completed"
        })
        
        hourly_success_rate = (successful_last_hour / tasks_last_hour * 100) if tasks_last_hour > 0 else 0
        
        text = f"""
📈 **Live System Dashboard**

**Real-time Metrics:**
⚡ Tasks Last Hour: {tasks_last_hour}
✅ Success Rate (1h): {hourly_success_rate:.1f}%
🎭 Active Reactions: {stats['daily_reactions']}
📡 Posts Processed: {stats['posts_processed_today']}

**System Status:**
🤖 Healthy Sessions: {stats['healthy_sessions']}/{stats['total_sessions']}
👥 Active Users: {stats['active_users']}
📺 Active Channels: {stats['active_channels']}
💰 System Credits: {stats['total_credits']:,}

**Recent Activity:**
📋 Latest Tasks: {len(recent_tasks)} in queue
📡 Latest Posts: {len(recent_posts)} discovered

**Performance Indicators:**
{"🟢 Excellent" if hourly_success_rate >= 95 else "🟡 Good" if hourly_success_rate >= 80 else "🔴 Poor"} - Reaction Success Rate
{"🟢 Optimal" if stats['healthy_sessions'] >= 5 else "🟡 Limited" if stats['healthy_sessions'] >= 2 else "🔴 Critical"} - Session Availability
{"🟢 Active" if tasks_last_hour >= 10 else "🟡 Moderate" if tasks_last_hour >= 1 else "🔴 Idle"} - System Activity
"""
        
        keyboard = [
            [
                InlineKeyboardButton("🔄 Refresh Dashboard", callback_data="admin:dashboard"),
                InlineKeyboardButton("📊 Detailed Analytics", callback_data="admin:analytics")
            ],
            [
                InlineKeyboardButton("🚨 Emergency Controls", callback_data="admin:emergency"),
                InlineKeyboardButton("⚙️ System Settings", callback_data="admin:settings")
            ],
            [
                InlineKeyboardButton("🔙 Back to Admin Panel", callback_data="admin:main")
            ]
        ]
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
    
    async def show_emergency_controls(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show emergency control panel"""
        
        query = update.callback_query
        
        text = """
🚨 **Emergency Controls**

**System Emergency Actions:**

⚠️ **Warning:** These actions affect the entire system.
Use only when necessary and with caution.

**Available Emergency Actions:**
• Pause all reaction processing
• Emergency session restart
• System maintenance mode
• Emergency credit freeze
• Bulk user notifications
• System shutdown procedures

**Current System Status:**
🟢 All systems operational
"""
        
        keyboard = [
            [
                InlineKeyboardButton("⏸️ Pause All Processing", callback_data="admin:emergency_pause"),
                InlineKeyboardButton("🔄 Restart Sessions", callback_data="admin:emergency_restart")
            ],
            [
                InlineKeyboardButton("🔧 Maintenance Mode", callback_data="admin:emergency_maintenance"),
                InlineKeyboardButton("❄️ Freeze Credits", callback_data="admin:emergency_freeze")
            ],
            [
                InlineKeyboardButton("📢 Emergency Broadcast", callback_data="admin:emergency_broadcast"),
                InlineKeyboardButton("🛑 System Shutdown", callback_data="admin:emergency_shutdown")
            ],
            [
                InlineKeyboardButton("🔙 Back to Admin Panel", callback_data="admin:main")
            ]
        ]
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
    
    async def handle_admin_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle admin panel callback queries"""
        
        query = update.callback_query
        data = query.data
        user_id = update.effective_user.id
        
        if user_id not in self.admin_user_ids:
            await query.answer("❌ Access denied")
            return
        
        # Route to appropriate handler
        if data == "admin:main":
            await self.show_admin_panel(update, context)
        elif data == "admin:users":
            await self.show_user_management(update, context)
        elif data == "admin:sessions":
            await self.show_session_management(update, context)
        elif data == "admin:credits":
            await self.show_credit_management(update, context)
        elif data == "admin:dashboard":
            await self.show_live_dashboard(update, context)
        elif data == "admin:emergency":
            await self.show_emergency_controls(update, context)
        elif data == "admin:health_check":
            await self.perform_health_check(update, context)
        else:
            await query.edit_message_text("🚧 Feature implementation in progress...")
    
    async def perform_health_check(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Perform system-wide health check"""
        
        query = update.callback_query
        
        await query.edit_message_text("🔄 Performing system health check...")
        
        # Perform health check
        await self.userbot_manager.health_check_all_sessions()
        
        # Get updated statistics
        session_stats = await self.userbot_manager.get_session_statistics()
        
        text = f"""
✅ **Health Check Complete**

**Results:**
🤖 Total Sessions: {session_stats['total_sessions']}
💚 Healthy Sessions: {session_stats['healthy_sessions']}
🔄 Loaded Sessions: {session_stats['loaded_sessions']}
⚠️ Issues Found: {session_stats['total_sessions'] - session_stats['healthy_sessions']}

**Actions Taken:**
• Verified all session connections
• Updated health status
• Removed unhealthy sessions
• Reloaded failed sessions where possible

**Recommendation:**
{"🟢 System operating normally" if session_stats['healthy_sessions'] >= 3 else "⚠️ Consider adding more sessions" if session_stats['healthy_sessions'] >= 1 else "🔴 Critical: No healthy sessions available"}
"""
        
        keyboard = [
            [InlineKeyboardButton("🔄 Run Again", callback_data="admin:health_check")],
            [InlineKeyboardButton("🤖 Session Management", callback_data="admin:sessions")],
            [InlineKeyboardButton("🔙 Back to Admin Panel", callback_data="admin:main")]
        ]
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
